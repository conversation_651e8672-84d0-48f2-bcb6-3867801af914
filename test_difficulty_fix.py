#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the difficulty calibration fix
"""

import sys
import os
sys.path.append('R1-Omni-main/src/r1-v/src')

from open_r1.trainer.difficulty_calibration import DifficultyCalibrator

def test_difficulty_persistence():
    """Test that difficulty changes persist across batches"""
    
    print("🧪 Testing difficulty calibration persistence fix...")
    
    # Initialize calibrator
    calibrator = DifficultyCalibrator(
        diff_threshold=0.7,
        window_size=10,
        min_samples=1,
        update_frequency=1,
        smoothing_factor=0.1
    )
    
    # Set initial difficulties
    calibrator.id2diff = {
        'sample_1': 'hard',
        'sample_2': 'hard', 
        'sample_3': 'hard',
        'sample_4': 'hard',
        'sample_5': 'hard'
    }
    
    print(f"Initial state: {calibrator.get_difficulty_distribution()}")
    
    # First batch: sample_1 performs well and should become easy
    print("\n--- First Batch ---")
    sample_ids_batch1 = ['sample_1', 'sample_2']
    accuracies_batch1 = [0.8, 0.5]  # sample_1 above threshold, sample_2 below
    
    calibrator.update_accuracy(sample_ids_batch1, accuracies_batch1)
    updated = calibrator.update_difficulties()
    
    print(f"Updated difficulties: {updated}")
    print(f"Current distribution: {calibrator.get_difficulty_distribution()}")
    
    # Second batch: different samples, sample_1 not included
    print("\n--- Second Batch ---")
    sample_ids_batch2 = ['sample_3', 'sample_4']
    accuracies_batch2 = [0.6, 0.4]  # Both below threshold
    
    calibrator.update_accuracy(sample_ids_batch2, accuracies_batch2)
    updated = calibrator.update_difficulties()
    
    print(f"Updated difficulties: {updated}")
    print(f"Current distribution: {calibrator.get_difficulty_distribution()}")
    
    # Third batch: sample_1 appears again (should still be easy)
    print("\n--- Third Batch ---")
    sample_ids_batch3 = ['sample_1', 'sample_5']
    accuracies_batch3 = [0.9, 0.8]  # Both above threshold
    
    calibrator.update_accuracy(sample_ids_batch3, accuracies_batch3)
    updated = calibrator.update_difficulties()
    
    print(f"Updated difficulties: {updated}")
    print(f"Current distribution: {calibrator.get_difficulty_distribution()}")
    
    # Verify sample_1 is still easy
    assert calibrator.get_difficulty('sample_1') == 'easy', "sample_1 should remain easy"
    assert calibrator.get_difficulty('sample_5') == 'easy', "sample_5 should become easy"
    
    print("\n✅ Test passed! Difficulty persistence works correctly.")
    
    # Debug detailed status
    calibrator.debug_sample_status()

if __name__ == "__main__":
    test_difficulty_persistence()
