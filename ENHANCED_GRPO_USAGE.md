# Enhanced GRPO with Difficulty-aware Length Reward

## 概述

已成功集成AdaCtrl的easy-hard GRPO算法到R1-Omni项目，并实现了Difficulty-aware Length Reward功能。

## 主要特性

### ✅ 已实现功能

1. **增强奖励函数** - 组合了格式奖励、准确度奖励和难度感知长度奖励
2. **难度校准机制** - 根据GRPO组的准确率动态更新难度标签
3. **Difficulty-aware Length Reward** - 实现了论文中的公式
4. **权重系数调节** - 支持自定义各组件的权重
5. **开放词汇评估** - 基于EW评估的准确度计算

### 🎯 奖励函数组成

```
总奖励 = format_weight × 格式奖励 + accuracy_weight × 准确度奖励 + length_weight × 长度奖励
```

**默认权重配置：**
- 格式奖励权重：0.2 (20%)
- 准确度奖励权重：0.6 (60%) 
- 长度奖励权重：0.2 (20%)

### 📐 Difficulty-aware Length Reward公式

**Easy问题：**
```
rl(yi) = 1.0 - (1 - cos((li^j / Li) * π)) / 2
```

**Hard问题：**
```
rl(yi) = 0.0
```

其中：
- `li^j` 是第j个回答的长度
- `Li` 是rollout组中的最大长度
- 难度标签基于组准确率和阈值(默认0.6)动态更新

## 使用方法

### 1. 启动训练

```bash
cd R1-Omni-main/src/r1-v/src/open_r1
chmod +x run_emotion_grpo.sh
./run_emotion_grpo.sh
```

### 2. 配置参数

在 `run_emotion_grpo.sh` 中可以调整以下参数：

```bash
# 增强奖励函数参数 (always enabled)
ENHANCED_FORMAT_WEIGHT=0.2
ENHANCED_ACCURACY_WEIGHT=0.6
ENHANCED_LENGTH_WEIGHT=0.2
DIFFICULTY_THRESHOLD=0.6

# 训练参数
BATCH_SIZE=1
LEARNING_RATE=5e-6
NUM_EPOCHS=1
```

### 3. 监控训练

训练过程中会显示：
- 奖励组件分解 (format/accuracy/length)
- 难度校准统计
- 组准确率和难度标签更新

## 文件结构

```
R1-Omni-main/src/r1-v/src/open_r1/
├── train_emotion_easy_hard_grpo.py     # 主训练脚本
├── trainer/
│   ├── open_vocab_reward.py            # 增强奖励函数
│   ├── humanOmni_grpo_trainer.py       # GRPO训练器
│   └── difficulty_calibration.py       # 难度校准
├── run_emotion_grpo.sh                 # 启动脚本
└── emotion_grpo_dataset_fixed.json     # 训练数据集
```

## 核心改进

### 1. 删除了简单类别匹配
- 移除了备用的简单情感匹配逻辑
- 专注于基于EW评估的开放词汇匹配

### 2. 实现了完整的Difficulty-aware Length Reward
- 按照论文公式实现
- 支持动态难度校准
- 鼓励easy问题简洁回答，保持hard问题长思考

### 3. 权重系数调节
- 支持运行时配置权重
- 灵活的奖励组合策略

### 4. 集成到现有训练流程
- 无缝集成到R1-Omni的GRPO训练
- 保持原有的分布式训练支持

## 预期效果

1. **Easy问题**：模型会倾向于生成更简洁的回答
2. **Hard问题**：模型保持详细的思考过程
3. **整体性能**：在保持准确率的同时优化回答长度
4. **动态适应**：根据模型表现自动调整难度标签

## 调试和监控

训练日志中会显示：
```
🎯 Reward breakdown: format=1.000, accuracy=0.000, length=0.841, total=0.468
🎯 Group test_group: avg_acc=0.813, difficulty=easy
📏 Length reward: len=6, max_len=23, ratio=0.261, reward=0.841
```

## 故障排除

1. **导入错误**：确保所有路径正确设置
2. **CUDA内存不足**：减少batch_size或gradient_accumulation_steps
3. **数据集问题**：检查emotion_grpo_dataset_fixed.json是否存在

## 下一步

训练完成后可以：
1. 分析难度校准统计
2. 评估不同难度问题的回答质量
3. 调整权重参数优化性能
4. 在测试集上验证效果
