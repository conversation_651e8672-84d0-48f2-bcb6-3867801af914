# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import textwrap
import numpy as np
from collections import defaultdict
from typing import Any, Callable, Optional, Union
from qwen_vl_utils import process_vision_info
import torch
import torch.utils.data
import transformers
from datasets import Dataset, IterableDataset
from packaging import version
from transformers import (
    AriaForConditionalGeneration,
    AriaProcessor,
    AutoModelForCausalLM,
    AutoModelForSequenceClassification,
    AutoProcessor,
    AutoTokenizer,
    GenerationConfig,
    PreTrainedModel,
    PreTrainedTokenizerBase,
    Qwen2VLForConditionalGeneration,
    Qwen2_5_VLForConditionalGeneration,
    Trainer,
    TrainerCallback,
    is_wandb_available,
)
from transformers.integrations.deepspeed import is_deepspeed_zero3_enabled
from transformers.utils import is_peft_available

from trl.data_utils import apply_chat_template, is_conversational, maybe_apply_chat_template
from trl.models import create_reference_model, prepare_deepspeed, unwrap_model_for_generation
from trl.trainer.grpo_config import GRPOConfig
from trl.trainer.utils import generate_model_card, get_comet_experiment_url

# Import AdaCtrl core algorithms for easy-hard GRPO
from .adactrl_core_algos import (
    compute_grpo_outcome_advantage,
    extract_think_answer_content,
    compute_format_reward,
    AdaptiveKLController
)

# Import difficulty calibration
from .difficulty_calibration import (
    DifficultyCalibrator,
    extract_predicted_difficulty
)

import copy
from transformers import BertModel, BertTokenizer
from humanomni.model import *
from humanomni.constants import NUM_FRAMES, IGNORE_INDEX, MODAL_INDEX_MAP, DEFAULT_X_START_TOKEN, DEFAULT_X_END_TOKEN
from humanomni.mm_utils import tokenizer_multimodal_token, process_image, read_video_patch, process_audio, frame_sample,get_model_name_from_path
# Import fixed process_video function
from utils.video_utils import process_video_fixed
# Import fixed audio encoding function
from utils.audio_utils import encode_audios_fixed
from humanomni import model_init, mm_infer
from humanomni.constants import DEFAULT_IMAGE_TOKEN, DEFAULT_VIDEO_TOKEN
from transformers import (
    CLIPVisionModel, CLIPImageProcessor, CLIPVisionConfig,
    SiglipVisionModel, SiglipImageProcessor, SiglipVisionConfig,
     WhisperFeatureExtractor, WhisperProcessor, WhisperConfig, WhisperForAudioClassification
)
import os
import sys
from .grpo_trainer import Qwen2VLGRPOTrainer
# sys.path.append('/mnt/data/jiaxing.zjx/code/HumanOmni/')
# sys.path.append('/mnt/data/jiaxing.zjx/cache/huggingface/')
#初始化BERT分词器
# bert_model = "bert-base-uncased"
# bert_tokenizer = BertTokenizer.from_pretrained(bert_model)

def check_parameters(model):
    frozen_params = []
    trainable_params = []

    for name, param in model.named_parameters():
        if param.requires_grad:
            trainable_params.append(name)
        else:
            frozen_params.append(name)

    print("Frozen Parameters:")
    for name in frozen_params:
        print(name)
    
    print("\nTrainable Parameters:")
    for name in trainable_params:
        print(name)



if is_peft_available():
    from peft import PeftConfig, get_peft_model

if is_wandb_available():
    import wandb

# What we call a reward function is a callable that takes a list of prompts and completions and returns a list of
# rewards. When it's a string, it's a model ID, so it's loaded as a pretrained model.
RewardFunc = Union[str, PreTrainedModel, Callable[[list, list], list[float]]]

from moviepy.editor import VideoFileClip

def get_video_duration(video_path):
    """根据视频路径获取视频的时长"""
    try:
        clip = VideoFileClip(video_path)
        duration = clip.duration
        clip.close()
        return duration
    except Exception as e:
        print(f"Error reading video file {video_path}: {e}")
        return None

class HumanOmniVLGRPOTrainer(Trainer):
    """
    Trainer for the Group Relative Policy Optimization (GRPO) method. This algorithm was initially proposed in the
    paper [DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models](https://huggingface.co/papers/2402.03300).

    Example:

    ```python
    from datasets import load_dataset
    from trl import GRPOTrainer

    dataset = load_dataset("trl-lib/tldr", split="train")

    trainer = GRPOTrainer(
        model="Qwen/Qwen2-0.5B-Instruct",
        reward_funcs="weqweasdas/RM-Gemma-2B",
        train_dataset=dataset,
    )

    trainer.train()
    ```

    Args:
        model (`Union[str, PreTrainedModel]`):
            Model to be trained. Can be either:

            - A string, being the *model id* of a pretrained model hosted inside a model repo on huggingface.co, or
              a path to a *directory* containing model weights saved using
              [`~transformers.PreTrainedModel.save_pretrained`], e.g., `'./my_model_directory/'`. The model is
              loaded using [`~transformers.AutoModelForCausalLM.from_pretrained`] with the keywork arguments
              in `args.model_init_kwargs`.
            - A [`~transformers.PreTrainedModel`] object. Only causal language models are supported.
        reward_funcs (`Union[RewardFunc, list[RewardFunc]]`):
            Reward functions to be used for computing the rewards. To compute the rewards, we call all the reward
            functions with the prompts and completions and sum the rewards. Can be either:

            - A single reward function, such as:
                - A string: The *model ID* of a pretrained model hosted inside a model repo on huggingface.co, or a
                path to a *directory* containing model weights saved using
                [`~transformers.PreTrainedModel.save_pretrained`], e.g., `'./my_model_directory/'`. The model is loaded
                using [`~transformers.AutoModelForSequenceClassification.from_pretrained`] with `num_labels=1` and the
                keyword arguments in `args.model_init_kwargs`.
                - A [`~transformers.PreTrainedModel`] object: Only sequence classification models are supported.
                - A custom reward function: The function is provided with the prompts and the generated completions,
                  plus any additional columns in the dataset. It should return a list of rewards. For more details, see
                  [Using a custom reward function](#using-a-custom-reward-function).
            - A list of reward functions, where each item can independently be any of the above types. Mixing different
            types within the list (e.g., a string model ID and a custom reward function) is allowed.
        args ([`GRPOConfig`], *optional*, defaults to `None`):
            Configuration for this trainer. If `None`, a default configuration is used.
        train_dataset ([`~datasets.Dataset`] or [`~datasets.IterableDataset`]):
            Dataset to use for training. It must include a column `"prompt"`. Any additional columns in the dataset is
            ignored. The format of the samples can be either:

            - [Standard](dataset_formats#standard): Each sample contains plain text.
            - [Conversational](dataset_formats#conversational): Each sample contains structured messages (e.g., role
              and content).
        eval_dataset ([`~datasets.Dataset`], [`~datasets.IterableDataset`] or `dict[str, Union[Dataset, IterableDataset]]`):
            Dataset to use for evaluation. It must meet the same requirements as `train_dataset`.
        processing_class ([`~transformers.PreTrainedTokenizerBase`], *optional*, defaults to `None`):
            Processing class used to process the data. The padding side must be set to "left". If `None`, the
            processing class is loaded from the model's name with [`~transformers.AutoTokenizer.from_pretrained`].
        reward_processing_classes (`Union[PreTrainedTokenizerBase, list[PreTrainedTokenizerBase]]`, *optional*, defaults to `None`):
            Processing classes corresponding to the reward functions specified in `reward_funcs`. Can be either:

            - A single processing class: Used when `reward_funcs` contains only one reward function.
            - A list of processing classes: Must match the order and length of the reward functions in `reward_funcs`.
            If set to `None`, or if an element of the list corresponding to a [`~transformers.PreTrainedModel`] is
            `None`, the tokenizer for the model is automatically loaded using [`~transformers.AutoTokenizer.from_pretrained`].
            For elements in `reward_funcs` that are custom reward functions (not [`~transformers.PreTrainedModel`]),
            the corresponding entries in `reward_processing_classes` are ignored.
        callbacks (list of [`~transformers.TrainerCallback`], *optional*, defaults to `None`):
            List of callbacks to customize the training loop. Will add those to the list of default callbacks
            detailed in [here](https://huggingface.co/docs/transformers/main_classes/callback).

            If you want to remove one of the default callbacks used, use the [`~transformers.Trainer.remove_callback`]
            method.
        optimizers (`tuple[torch.optim.Optimizer, torch.optim.lr_scheduler.LambdaLR]`, *optional*, defaults to `(None, None)`):
            A tuple containing the optimizer and the scheduler to use. Will default to an instance of [`AdamW`] on your
            model and a scheduler given by [`get_linear_schedule_with_warmup`] controlled by `args`.
        peft_config ([`~peft.PeftConfig`], *optional*, defaults to `None`):
            PEFT configuration used to wrap the model. If `None`, the model is not wrapped.
    """

    def __init__(
        self,
        model: Union[str, PreTrainedModel],
        reward_funcs: Union[RewardFunc, list[RewardFunc]],
        args: GRPOConfig = None,
        train_dataset: Optional[Union[Dataset, IterableDataset]] = None,
        eval_dataset: Optional[Union[Dataset, IterableDataset, dict[str, Union[Dataset, IterableDataset]]]] = None,
        processing_class: Optional[PreTrainedTokenizerBase] = None,
        reward_processing_classes: Optional[Union[PreTrainedTokenizerBase, list[PreTrainedTokenizerBase]]] = None,
        callbacks: Optional[list[TrainerCallback]] = None,
        optimizers: tuple[Optional[torch.optim.Optimizer], Optional[torch.optim.lr_scheduler.LambdaLR]] = (None, None),
        peft_config: Optional["PeftConfig"] = None,
        max_pixels: Optional[int] = 12845056,
        min_pixels: Optional[int] = 3136,
        attn_implementation: str = "flash_attention_2",
    ):
        # Args
       # import ipdb;ipdb.set_trace()
        if args is None:
            model_name = model if isinstance(model, str) else model.config._name_or_path
            model_name = model_name.split("/")[-1]
            args = GRPOConfig(f"{model_name}-GRPO")

        # Models
        # Trained model
        model_init_kwargs = args.model_init_kwargs or {}
        model_init_kwargs["attn_implementation"] = attn_implementation
        if isinstance(model, str):
            model_id = model
            model_name_d = model_id
            torch_dtype = model_init_kwargs.get("torch_dtype")
            if isinstance(torch_dtype, torch.dtype) or torch_dtype == "auto" or torch_dtype is None:
                pass  # torch_dtype is already a torch.dtype or "auto" or None
            elif isinstance(torch_dtype, str):  # it's a str, but not "auto"
                torch_dtype = getattr(torch, torch_dtype)
                model_init_kwargs["torch_dtype"] = torch_dtype
            else:
                raise ValueError(
                    "Invalid `torch_dtype` passed to `GRPOConfig`. Expected either 'auto' or a string representing "
                    f"a `torch.dtype` (e.g., 'float32'), but got {torch_dtype}."
                )
            # Disable caching if gradient checkpointing is enabled (not supported)
            model_init_kwargs["use_cache"] = (
                False if args.gradient_checkpointing else model_init_kwargs.get("use_cache")
            )
            config = VLLMConfigs["HumanOmni_qwen2"].from_pretrained(
                model, 
                trust_remote_code=True
            )
            config.mm_vision_tower = '/data/wuyang/PLM/siglip-base-patch16-224'
            config.mm_audio_tower = '/data/wuyang/PLM/whisper-large-v3'
            model = VLLMs["HumanOmni_qwen2"].from_pretrained(
                model,
                config=config,
                cache_dir=None,
                torch_dtype=torch.bfloat16,
                do_sample=True
            )
            vision_tower = model.get_vision_tower()
            if not vision_tower.is_loaded:
                vision_tower.load_model()

            audio_tower = model.get_audio_tower()
            if not audio_tower.is_loaded:
                audio_tower.load_model()

            # Use the processors from the loaded towers
            self.visual_processor = vision_tower.image_processor
            # Import and use the audio processor
            from transformers import WhisperFeatureExtractor
            self.audio_processor = WhisperFeatureExtractor.from_pretrained(config.mm_audio_tower)

        else:
            model_id = model.config._name_or_path
            config = model.config  # Use the existing model's config
            if args.model_init_kwargs is not None:
                raise ValueError(
                    "You passed `model_init_kwargs` to the `GRPOConfig`, but your model is already instantiated. "
                    "This argument can only be used when the `model` argument is a string."
                )

        if peft_config is not None:
            model = get_peft_model(model, peft_config)

        # Reference model
        # Use the same model path as the main model
        model_path = model if isinstance(model, str) else model.config._name_or_path

        self.ref_model = VLLMs["HumanOmni_qwen2"].from_pretrained(
            model_path,
            config=config,
            cache_dir=None,
            torch_dtype=torch.bfloat16,
            do_sample=True
        )
        vision_tower = self.ref_model.get_vision_tower()
        if not vision_tower.is_loaded:
            vision_tower.load_model()

        audio_tower = self.ref_model.get_audio_tower()
        if not audio_tower.is_loaded:
            audio_tower.load_model()



        bert_model = "/data/wuyang/PLM/bert-base-uncased"
        self.bert_tokenizer = BertTokenizer.from_pretrained(bert_model)


        # Processing class - use HumanOmni standard processors
        if processing_class is None:
            from transformers import AutoTokenizer
            processing_class = AutoTokenizer.from_pretrained("/data/wuyang/PLM/Qwen2-VL-2B-Instruct")
            pad_token_id = processing_class.pad_token_id
        else:
            pad_token_id = getattr(processing_class, 'pad_token_id', 0)

        # Set standard HumanOmni processors
        from transformers import SiglipImageProcessor, WhisperFeatureExtractor
        self.visual_processor = SiglipImageProcessor.from_pretrained("/data/wuyang/PLM/siglip-base-patch16-224")
        self.audio_processor = WhisperFeatureExtractor.from_pretrained("/data/wuyang/PLM/whisper-large-v3")

        # Reward functions
        if not isinstance(reward_funcs, list):
            reward_funcs = [reward_funcs]
        for i, reward_func in enumerate(reward_funcs):
            if isinstance(reward_func, str):
                reward_funcs[i] = AutoModelForSequenceClassification.from_pretrained(
                    reward_func, num_labels=1, **model_init_kwargs
                )
        self.reward_funcs = reward_funcs

        # Reward processing class
        if reward_processing_classes is None:
            reward_processing_classes = [None] * len(reward_funcs)
        elif not isinstance(reward_processing_classes, list):
            reward_processing_classes = [reward_processing_classes]
        else:
            if len(reward_processing_classes) != len(reward_funcs):
                raise ValueError("The number of reward processing classes must match the number of reward functions.")

        for i, (reward_processing_class, reward_func) in enumerate(zip(reward_processing_classes, reward_funcs)):
            if isinstance(reward_func, PreTrainedModel):
                if reward_processing_class is None:
                    reward_processing_class = AutoTokenizer.from_pretrained(reward_func.config._name_or_path)
                if reward_processing_class.pad_token_id is None:
                    reward_processing_class.pad_token = reward_processing_class.eos_token
                # The reward model computes the reward for the latest non-padded token in the input sequence.
                # So it's important to set the pad token ID to the padding token ID of the processing class.
                reward_func.config.pad_token_id = reward_processing_class.pad_token_id
                reward_processing_classes[i] = reward_processing_class
        self.reward_processing_classes = reward_processing_classes

        # Data collator
        def data_collator(features):  # No data collation is needed in GRPO
            return features

        # Training arguments
        self.max_prompt_length = args.max_prompt_length
        self.max_completion_length = args.max_completion_length  # = |o_i| in the GRPO paper
        self.num_generations = args.num_generations  # = G in the GRPO paper
        self.generation_config = GenerationConfig(
            max_new_tokens=self.max_completion_length,
            do_sample=True,
            temperature=0.7, # HACK
            top_p=0.9,  # Nucleus sampling
            num_return_sequences=self.num_generations,
            pad_token_id=pad_token_id,
        )
        self.beta = args.beta

        # The trainer estimates the number of FLOPs (floating-point operations) using the number of elements in the
        # input tensor associated with the key "input_ids". However, in GRPO, the sampled data does not include the
        # "input_ids" key. Instead, the available keys is "prompt". As a result, the trainer issues the warning:
        # "Could not estimate the number of tokens of the input, floating-point operations will not be computed." To
        # suppress this warning, we set the "estimate_tokens" key in the model's "warnings_issued" dictionary to True.
        # This acts as a flag to indicate that the warning has already been issued.
        model.warnings_issued["estimate_tokens"] = True

        # Initialize the metrics
        self._metrics = defaultdict(list)

        super().__init__(
            model=model,
            args=args,
            data_collator=data_collator,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            processing_class=processing_class,
            callbacks=callbacks,
            optimizers=optimizers,
        )

        # Gradient accumulation requires scaled loss. Normally, loss scaling in the parent class depends on whether the
        # model accepts loss-related kwargs. Since we compute our own loss, this check is irrelevant. We set
        # self.model_accepts_loss_kwargs to False to enable scaling.
        self.model_accepts_loss_kwargs = False

        if self.ref_model is not None:
            if self.is_deepspeed_enabled:
                self.ref_model = prepare_deepspeed(self.ref_model, self.accelerator)
            else:
                self.ref_model = self.accelerator.prepare_model(self.ref_model, evaluation_mode=True)

        for i, reward_func in enumerate(self.reward_funcs):
            if isinstance(reward_func, PreTrainedModel):
                self.reward_funcs[i] = self.accelerator.prepare_model(reward_func, evaluation_mode=True)

        # ===== AdaCtrl Easy-Hard GRPO Configuration =====
        # Initialize easy-hard GRPO specific parameters
        self.enable_easy_hard_grpo = getattr(args, 'enable_easy_hard_grpo', True)
        self.format_reward_weight = getattr(args, 'format_reward_weight', 0.1)
        self.length_reward_weight = getattr(args, 'length_reward_weight', 0.1)
        self.max_response_length = getattr(args, 'max_response_length', 1024)

        # Initialize adaptive KL controller if enabled
        if hasattr(args, 'adaptive_kl') and args.adaptive_kl:
            self.kl_controller = AdaptiveKLController(
                init_kl_coef=getattr(args, 'init_kl_coef', 0.1),
                target_kl=getattr(args, 'target_kl', 0.1),
                horizon=getattr(args, 'kl_horizon', 10000)
            )
        else:
            self.kl_controller = None

        # Initialize difficulty calibrator if enabled
        self.enable_difficulty_calibration = getattr(args, 'enable_difficulty_calibration', False)
        if self.enable_difficulty_calibration:
            self.difficulty_calibrator = DifficultyCalibrator(
                diff_threshold=getattr(args, 'diff_threshold', 0.5),
                update_frequency=getattr(args, 'difficulty_update_frequency', 100),
                window_size=getattr(args, 'difficulty_window_size', 10),
                min_samples=getattr(args, 'difficulty_min_samples', 3),
                smoothing_factor=getattr(args, 'difficulty_smoothing_factor', 0.1)
            )

            # Load initial difficulties if data path is provided
            difficulty_data_path = getattr(args, 'difficulty_data_path', None)
            if difficulty_data_path:
                self.difficulty_calibrator.load_initial_difficulties(difficulty_data_path)
        else:
            self.difficulty_calibrator = None

        # ===== Difficulty Calibration =====
        # Initialize difficulty calibrator if enabled
        self.enable_difficulty_calibration = getattr(args, 'enable_difficulty_calibration', True)
        if self.enable_difficulty_calibration:
            self.difficulty_calibrator = DifficultyCalibrator(
                diff_threshold=getattr(args, 'diff_threshold', 0.6),
                window_size=getattr(args, 'diff_window_size', 8),  # GRPO group size
                min_samples=getattr(args, 'diff_min_samples', 1),  # No minimum for GRPO
                update_frequency=1,  # Not used anymore - always update after each GRPO group
                smoothing_factor=getattr(args, 'diff_smoothing_factor', 0.1)
            )

            # Load initial difficulties from data file if provided
            data_path = getattr(args, 'difficulty_data_path', '/data/wuyang/MERGE_GRPO/sample_results.csv')
            if data_path:
                self.difficulty_calibrator.load_initial_difficulties(data_path)
        else:
            self.difficulty_calibrator = None

        # ===== Apply Audio Encoding Fix =====
        # Apply monkey patch to fix audio encoding dtype issue for all models
        def create_patched_encode_audios(model_instance):
            def patched_encode_audios(audios):
                return encode_audios_fixed(model_instance, audios)
            return patched_encode_audios

        # Patch the main model
        if hasattr(self.model, 'encode_audios'):
            self.model.encode_audios = create_patched_encode_audios(self.model)

        # Patch the reference model
        if hasattr(self.ref_model, 'encode_audios'):
            self.ref_model.encode_audios = create_patched_encode_audios(self.ref_model)

    def _set_signature_columns_if_needed(self):
        # If `self.args.remove_unused_columns` is True, non-signature columns are removed.
        # By default, this method sets `self._signature_columns` to the model's expected inputs.
        # In GRPOTrainer, we preprocess data, so using the model's signature columns doesn't work.
        # Instead, we set them to the columns expected by the `training_step` method, hence the override.
        if self._signature_columns is None:
            self._signature_columns = ["prompt"]


    # Get the per-token log probabilities for the completions for the model and the reference model
    def _get_per_token_logps(self, model, input_ids, attention_mask, pixel_values, image_grid_thw):
        logits = model(input_ids, attention_mask=attention_mask, pixel_values=pixel_values, image_grid_thw=image_grid_thw).logits  # (B, L, V)
        logits = logits[:, :-1, :]  # (B, L-1, V), exclude the last logit: it corresponds to the next token pred
        input_ids = input_ids[:, 1:]  # (B, L-1), exclude the first input ID since we don't have logits for it
        # Compute the log probabilities for the input tokens. Use a loop to reduce memory peak.
        per_token_logps = []
        for logits_row, input_ids_row in zip(logits, input_ids):
            log_probs = logits_row.log_softmax(dim=-1)
            token_log_prob = torch.gather(log_probs, dim=1, index=input_ids_row.unsqueeze(1)).squeeze(1)
            per_token_logps.append(token_log_prob)
        return torch.stack(per_token_logps)
    
    def _get_per_token_logps_video(self, model, input_ids, attention_mask, images, audios,prompts, answer_length ):
        logits = model(input_ids, attention_mask=attention_mask, images=images, audios=audios, prompts=prompts).logits  # (B, L, V)
       # import ipdb;ipdb.set_trace()
        logits = logits[:, :-1, :]  # (B, L-1, V), exclude the last logit: it corresponds to the next token pred
        input_ids = input_ids[:, 1:]  # (B, L-1), exclude the first input ID since we don't have logits for it
        logits = logits[:, (-answer_length) :]
        input_ids = input_ids[:, -(answer_length) :]
        # Compute the log probabilities for the input tokens. Use a loop to reduce memory peak.
        per_token_logps = []
        for logits_row, input_ids_row in zip(logits, input_ids):
            log_probs = logits_row.log_softmax(dim=-1)
            token_log_prob = torch.gather(log_probs, dim=1, index=input_ids_row.unsqueeze(1)).squeeze(1)
            per_token_logps.append(token_log_prob)
        return torch.stack(per_token_logps)


    # Trainer "prepares" the inputs before calling `compute_loss`. It converts to tensor and move to device.
    # Since we preprocess the data in `compute_loss`, we need to override this method to skip this step.
    def _prepare_inputs(self, inputs: dict[str, Union[torch.Tensor, Any]]) -> dict[str, Union[torch.Tensor, Any]]:
        return inputs
    
    def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
        if return_outputs:
            raise ValueError("The GRPOTrainer does not support returning outputs")

        # Ensure vision and audio towers are properly loaded before processing
        try:
            # Get the actual model (unwrap if needed)
            actual_model = model
            if hasattr(model, 'module'):
                actual_model = model.module

            # Check and load vision tower
            vision_tower = actual_model.get_vision_tower()
            if vision_tower is not None and not vision_tower.is_loaded:
                print("🔧 Loading vision tower during compute_loss...")
                vision_tower.load_model()

            # Check and load audio tower
            audio_tower = actual_model.get_audio_tower()
            if audio_tower is not None and not audio_tower.is_loaded:
                print("🔧 Loading audio tower during compute_loss...")
                audio_tower.load_model()

        except Exception as e:
            print(f"⚠️ Warning: Could not ensure towers are loaded: {e}")
            # Continue anyway, the forward method will handle it

        prompts = []
        bert_prompts = []
        for x in inputs:
            prompt = x["prompt"]

            # Extract text content for BERT processing
            # Now content is a string, not a list
            content_text = prompt[0]['content']
            # Remove the <video> and <audio> tags for BERT processing
            bert_text = content_text.replace('<video>\n<audio>\n', '')
            bert_prompts.append(bert_text)

            # Video path should be at top level now
            if "video" in x:
                video_path = x["video"]
            else:
                raise KeyError(f"No video path found in data item: {x}")

            # Update the prompt content to include the multimodal tokens
            # Remove original tokens and replace with wrapper tokens
            content_text_clean = content_text.replace('<video>\n<audio>\n', '')
            prompt[0]['content'] = '<vi_start><video><vi_end>\n<au_start><audio><au_end>\n' + content_text_clean
            prompts.append(prompt)

        bert_prompts = self.bert_tokenizer(bert_prompts, return_tensors='pt', padding=True, truncation=True,add_special_tokens=True)

        prompts_text = []
        for example in inputs:
            # Extract text from our data format instead of using chat template
            if 'prompt' in example and isinstance(example['prompt'], list):
                # Content is now a string, not a list
                content_text = example['prompt'][0]['content']
                # Remove the <video> and <audio> tags for text processing
                text_content = content_text.replace('<video>\n<audio>\n', '')
                prompts_text.append(text_content)
            else:
                # Fallback
                prompts_text.append("As an emotional recognition expert; throughout the video, which emotion conveyed by the characters is the most obvious to you?")

        # Use the tokenizer attribute if available, otherwise use processing_class directly
        tokenizer_to_use = getattr(self.processing_class, 'tokenizer', self.processing_class)
        input_ids = [tokenizer_multimodal_token(prompts_text_, tokenizer_to_use, '<video>', return_tensors='pt') for prompts_text_ in prompts_text]
        input_ids = torch.cat(input_ids, dim=0).unsqueeze(0)
        video = []
        audios = []
        for i, prompt in enumerate(prompts):
            # Get video file from the corresponding input data
            video_file = inputs[i]["video"]
            # Debug: print processor info
            print(f"Visual processor type: {type(self.visual_processor)}")
            print(f"Has preprocess method: {hasattr(self.visual_processor, 'preprocess')}")
            print(f"Has image_mean: {hasattr(self.visual_processor, 'image_mean')}")

            video_ids = process_video_fixed(video_file, self.visual_processor, aspect_ratio="pad", num_frames=8)
            # Debug: print video tensor shape safely
            if hasattr(video_ids, 'shape'):
                print(f"Video tensor shape: {video_ids.shape}")
            else:
                print(f"Video tensor type: {type(video_ids)}")
                # If it's a BatchFeature, extract the actual tensor
                if hasattr(video_ids, 'pixel_values'):
                    video_ids = video_ids.pixel_values
                    print(f"Extracted pixel_values shape: {video_ids.shape}")
                    # Convert to torch tensor if it's numpy
                    if isinstance(video_ids, np.ndarray):
                        video_ids = torch.from_numpy(video_ids)
                        print(f"Converted to torch tensor shape: {video_ids.shape}")
                elif hasattr(video_ids, 'data') and 'pixel_values' in video_ids.data:
                    video_ids = video_ids.data['pixel_values']
                    print(f"Extracted pixel_values from data shape: {video_ids.shape}")
                    # Convert to torch tensor if it's numpy
                    if isinstance(video_ids, np.ndarray):
                        video_ids = torch.from_numpy(video_ids)
                        print(f"Converted to torch tensor shape: {video_ids.shape}")
            # video_ids shape: [num_frames, channels, height, width] = [8, 3, 224, 224]
            video.append(video_ids)

            audio, audio_sample_rate = process_audio(video_file)
            # Use HumanOmni audio processor (function)
            audio_processed = self.audio_processor(audio)
            # HumanOmni audio processor might return a tuple, extract tensor
            if isinstance(audio_processed, tuple):
                audio = audio_processed[0]  # Take the first element if it's a tuple
            else:
                audio = audio_processed

            # Ensure audio tensor is float32 (required for audio tower input)
            if hasattr(audio, 'dtype') and audio.dtype != torch.float32:
                audio = audio.to(torch.float32)

            audios.append(audio)

        # Stack video tensors properly and ensure correct dtype
        # Handle different video tensor shapes (could be 2D features or 4D pixels)
        if len(video) > 1:
            # Check if all videos have the same shape
            shapes = [v.shape for v in video]
            if all(s == shapes[0] for s in shapes):
                video = torch.stack(video, dim=0)
            else:
                # If shapes are different, pad to the same size
                max_shape = [max(s[i] for s in shapes) for i in range(len(shapes[0]))]
                padded_videos = []
                for v in video:
                    if v.shape != tuple(max_shape):
                        # Pad the tensor to match max_shape
                        pad_sizes = []
                        for i in range(len(max_shape)):
                            pad_size = max_shape[i] - v.shape[i]
                            pad_sizes.extend([0, pad_size])
                        # Reverse pad_sizes for torch.nn.functional.pad
                        pad_sizes = pad_sizes[::-1]
                        v = torch.nn.functional.pad(v, pad_sizes)
                    padded_videos.append(v)
                video = torch.stack(padded_videos, dim=0)
        else:
            video = video[0].unsqueeze(0)

        # Ensure video tensor has the correct dtype (bfloat16 to match model)
        video = video.to(torch.bfloat16)

        # Stack audio tensors properly
        if len(audios) > 1:
            audios = torch.stack(audios, dim=0)
        else:
            audio_item = audios[0]
            # Handle BatchFeature objects for audio
            if hasattr(audio_item, 'input_features'):
                audio_item = audio_item.input_features
                print(f"Extracted audio input_features shape: {audio_item.shape}")
                # Convert to torch tensor if it's numpy
                if isinstance(audio_item, np.ndarray):
                    audio_item = torch.from_numpy(audio_item)
                    print(f"Converted audio to torch tensor shape: {audio_item.shape}")
            elif hasattr(audio_item, 'data') and hasattr(audio_item.data, 'get') and audio_item.data.get('input_features') is not None:
                audio_item = audio_item.data['input_features']
                print(f"Extracted audio input_features from data shape: {audio_item.shape}")
                # Convert to torch tensor if it's numpy
                if isinstance(audio_item, np.ndarray):
                    audio_item = torch.from_numpy(audio_item)
                    print(f"Converted audio to torch tensor shape: {audio_item.shape}")

            if hasattr(audio_item, 'unsqueeze'):
                audios = audio_item.unsqueeze(0)
            else:
                print(f"Audio item type: {type(audio_item)}")
                # If it's still not a tensor, try to convert it
                if not isinstance(audio_item, torch.Tensor):
                    audio_item = torch.tensor(audio_item)
                audios = audio_item.unsqueeze(0)

        # Ensure audio tensor is on the correct device and dtype
        # Move to the same device as the model
        device = next(model.parameters()).device
        audios = audios.to(device=device, dtype=torch.float32)

        attention_masks = input_ids.ne(self.processing_class.pad_token_id)
        prompt_inputs = {}
        prompt_inputs['inputs'] = input_ids
        prompt_inputs['images'] = video 
        prompt_inputs['attention_mask'] = attention_masks
        prompt_inputs['prompts'] = bert_prompts
        prompt_inputs['audios'] = audios

        prompt_inputs = super()._prepare_inputs(prompt_inputs)

        prompt_ids, prompt_mask = prompt_inputs["inputs"], prompt_inputs["attention_mask"]
  
        
        if self.max_prompt_length is not None:
            prompt_ids = prompt_ids[:, -self.max_prompt_length :]
            prompt_mask = prompt_mask[:, -self.max_prompt_length :]

        # Generate completions
        with unwrap_model_for_generation(model, self.accelerator) as unwrapped_model:
            prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
            prompt_length = prompt_ids.size(1)
            answer_length = prompt_completion_ids.size(1)
            completion_ids = prompt_completion_ids
            prompt_mask = prompt_mask.repeat_interleave(self.num_generations, dim=0)
            prompt_ids_repeat = prompt_ids.repeat_interleave(self.num_generations, dim=0)

        is_eos = completion_ids == self.processing_class.eos_token_id
        device = self.accelerator.device
        eos_idx = torch.full((is_eos.size(0),), is_eos.size(1), dtype=torch.long, device=device)
        eos_idx[is_eos.any(dim=1)] = is_eos.int().argmax(dim=1)[is_eos.any(dim=1)]
        sequence_indices = torch.arange(is_eos.size(1), device=device).expand(is_eos.size(0), -1)
        completion_mask = (sequence_indices <= eos_idx.unsqueeze(1)).int()


        attention_mask = torch.cat([prompt_mask, completion_mask], dim=1)  # (B*G, P+C)
        
        prompt_completion_ids_repeat = torch.cat([prompt_ids_repeat, prompt_completion_ids], dim=1)

        images_repeat = prompt_inputs['images'].repeat_interleave(self.num_generations, dim=0)
        audios_repeat = prompt_inputs['audios'].repeat_interleave(self.num_generations, dim=0)
        prompts_repeat = {}
        prompts_repeat['input_ids'] =  prompt_inputs['prompts']['input_ids'].repeat_interleave(self.num_generations, dim=0)
        prompts_repeat['token_type_ids'] =  prompt_inputs['prompts']['token_type_ids'].repeat_interleave(self.num_generations, dim=0)
        prompts_repeat['attention_mask'] =  prompt_inputs['prompts']['attention_mask'].repeat_interleave(self.num_generations, dim=0)
      

        per_token_logps = self._get_per_token_logps_video(model, prompt_completion_ids_repeat, attention_mask, images_repeat, audios_repeat, prompts_repeat, answer_length)

        per_token_logps = per_token_logps
  

        with torch.inference_mode():
            if self.ref_model is not None:
                ref_per_token_logps = self._get_per_token_logps_video(self.ref_model, prompt_completion_ids_repeat, attention_mask, images_repeat, audios_repeat, prompts_repeat, answer_length )
            else:
                with self.accelerator.unwrap_model(model).disable_adapter():
                    if use_image:
                        ref_per_token_logps = self._get_per_token_logps(model, prompt_completion_ids, attention_mask, pixel_values, image_grid_thw)
                    if use_video:
                        ref_per_token_logps = self._get_per_token_logps_video(model, prompt_completion_ids, attention_mask, pixel_values_videos, video_grid_thw)
        ref_per_token_logps = ref_per_token_logps

        per_token_kl = torch.exp(ref_per_token_logps - per_token_logps) - (ref_per_token_logps - per_token_logps) - 1

        # Decode the generated completions
        completions = self.processing_class.batch_decode(completion_ids, skip_special_tokens=True)
        if is_conversational(inputs[0]):
            completions = [[{"role": "assistant", "content": completion}] for completion in completions]

        # Compute the rewards
        prompts = [prompt for prompt in prompts for _ in range(self.num_generations)]

        rewards_per_func = torch.zeros(len(prompts), len(self.reward_funcs), device=device)
        for i, (reward_func, reward_processing_class) in enumerate(
            zip(self.reward_funcs, self.reward_processing_classes)
        ):
            if isinstance(reward_func, PreTrainedModel):
                if is_conversational(inputs[0]):
                    messages = [{"messages": p + c} for p, c in zip(prompts, completions)]
                    texts = [apply_chat_template(x, reward_processing_class)["text"] for x in messages]
                else:
                    texts = [p + c for p, c in zip(prompts, completions)]
                reward_inputs = reward_processing_class(
                    texts, return_tensors="pt", padding=True, padding_side="right", add_special_tokens=False
                )
                reward_inputs = super()._prepare_inputs(reward_inputs)
                with torch.inference_mode():
                    rewards_per_func[:, i] = reward_func(**reward_inputs).logits[:, 0]  # Shape (B*G,)
            else:
                # Repeat all input columns (but "prompt" and "completion") to match the number of generations
                reward_kwargs = {key: [] for key in inputs[0].keys() if key not in ["prompt", "completion"]}

                for key in reward_kwargs:
                    for example in inputs:
                        # Repeat each value in the column for `num_generations` times
                        reward_kwargs[key].extend([example[key]] * self.num_generations)

                # ===== Add difficulty calibrator information to reward kwargs =====
                if self.difficulty_calibrator:
                    # Extract sample IDs from inputs for difficulty lookup
                    sample_ids = []
                    difficulty_labels = []

                    for example in inputs:
                        for _ in range(self.num_generations):
                            # Extract sample ID from the example - id is a top-level field
                            sample_id = example.get('id')
                            if not sample_id:
                                raise ValueError(f"Sample missing required 'id' field: {example}")
                            sample_ids.append(sample_id)

                            # Get current difficulty from calibrator
                            difficulty = self.difficulty_calibrator.get_difficulty(sample_id)
                            difficulty_labels.append(difficulty)

                    # Add difficulty information to reward kwargs
                    reward_kwargs.update({
                        'sample_ids': sample_ids,
                        'difficulty_labels': difficulty_labels,
                        'difficulty_calibrator': self.difficulty_calibrator
                    })

                    # print(f"🔧 [REWARD KWARGS] Added difficulty info: {len(sample_ids)} sample_ids, {len(difficulty_labels)} labels")
                    # print(f"   Sample IDs: {sample_ids[:5]}..." if len(sample_ids) > 5 else f"   Sample IDs: {sample_ids}")
                    # print(f"   Difficulty labels: {difficulty_labels[:5]}..." if len(difficulty_labels) > 5 else f"   Difficulty labels: {difficulty_labels}")

                output_reward_func = reward_func(prompts=prompts, completions=completions, **reward_kwargs)

                # Handle both old format (just rewards) and new format (rewards + accuracy)
                # Note: We expect only rewards from configured_enhanced_reward since it returns just rewards
                rewards_list = output_reward_func
                # print(f"🔍 [REWARD EXTRACTION] Got rewards: {len(rewards_list)} rewards")

                # Ensure the reward tensor has the correct shape
                reward_tensor = torch.tensor(rewards_list, dtype=torch.float32, device=device)
                if reward_tensor.shape[0] != rewards_per_func.shape[0]:
                    print(f"Warning: Reward function returned {reward_tensor.shape[0]} rewards, expected {rewards_per_func.shape[0]}")
                    # If the reward function returned fewer rewards, repeat them
                    if reward_tensor.shape[0] < rewards_per_func.shape[0]:
                        repeat_factor = rewards_per_func.shape[0] // reward_tensor.shape[0]
                        reward_tensor = reward_tensor.repeat(repeat_factor)
                    # If still not matching, truncate or pad
                    if reward_tensor.shape[0] != rewards_per_func.shape[0]:
                        reward_tensor = reward_tensor[:rewards_per_func.shape[0]]
                rewards_per_func[:, i] = reward_tensor

        # Sum the rewards from all reward functions
        rewards = rewards_per_func.sum(dim=1)

        # ===== AdaCtrl Easy-Hard GRPO Advantage Computation =====
        # Extract completion texts for analysis
        completion_texts = []
        if is_conversational(inputs[0]):
            completion_texts = [comp[0]["content"] for comp in completions]
        else:
            completion_texts = [self.processing_class.batch_decode([comp_ids], skip_special_tokens=True)[0]
                              for comp_ids in completion_ids]

        # Create token-level rewards tensor (rewards applied at end of sequence)
        # Note: completion_texts length is batch_size * num_generations
        total_completions = len(completion_texts)
        response_length = completion_mask.shape[1]
        token_level_rewards = torch.zeros(total_completions, response_length, device=device)

        # Apply rewards at the end of each sequence
        completion_lengths = completion_mask.sum(dim=1)
        for i in range(total_completions):
            if completion_lengths[i] > 0:
                token_level_rewards[i, completion_lengths[i] - 1] = rewards[i]

        # Create prompt indices for grouping (each group has num_generations responses)
        prompt_indices = torch.arange(len(inputs), device=device).repeat_interleave(self.num_generations)

        # Debug GRPO input data
        # print(f"\n🔍 [GRPO INPUT DEBUG]")
        # print(f"   Token-level rewards shape: {token_level_rewards.shape}")
        # print(f"   Prompt indices: {prompt_indices.cpu().tolist()}")

        # Extract scores from token_level_rewards (same as GRPO function does)
        debug_scores = token_level_rewards.sum(dim=-1)
        # print(f"   Extracted scores: {[f'{s:.3f}' for s in debug_scores.cpu().tolist()]}")

        # Group by prompt index
        from collections import defaultdict
        id2score_debug = defaultdict(list)
        for i in range(debug_scores.shape[0]):
            id2score_debug[prompt_indices[i].item()].append(debug_scores[i].item())

        for idx in id2score_debug:
            group_scores = id2score_debug[idx]
            if len(group_scores) > 1:
                mean_score = sum(group_scores) / len(group_scores)
                std_score = torch.std(torch.tensor(group_scores)).item()
                # print(f"   Group {idx}: scores={[f'{s:.3f}' for s in group_scores]}")
                # print(f"      Mean: {mean_score:.6f}, Std: {std_score:.6f}")

                # Manual advantage calculation
                manual_advantages = [(s - mean_score) / (std_score + 1e-6) for s in group_scores]
                # print(f"      Manual advantages: {[f'{a:.3f}' for a in manual_advantages]}")

        # Compute GRPO outcome advantages using AdaCtrl algorithm
        grpo_advantages, grpo_returns = compute_grpo_outcome_advantage(
            token_level_rewards=token_level_rewards,
            eos_mask=completion_mask,
            index=prompt_indices
        )

        # Debug GRPO advantage calculation
        # print(f"\n🔍 [GRPO ADVANTAGE DEBUG]")
        scores = token_level_rewards.sum(dim=-1)
        # print(f"   Token-level rewards shape: {token_level_rewards.shape}")
        # print(f"   Completion scores: {[f'{s:.3f}' for s in scores.cpu().tolist()]}")
        # print(f"   Prompt indices: {prompt_indices.cpu().tolist()}")
        # print(f"   GRPO advantages (per-token): {grpo_advantages.sum(dim=1).cpu().tolist()}")

        # Group analysis
        from collections import defaultdict
        id2score = defaultdict(list)
        for i in range(scores.shape[0]):
            id2score[prompt_indices[i].item()].append(scores[i].item())

        for idx in id2score:
            group_scores = id2score[idx]
            if len(group_scores) > 1:
                mean_score = sum(group_scores) / len(group_scores)
                std_score = torch.std(torch.tensor(group_scores)).item()
                # print(f"   Group {idx}: scores={[f'{s:.3f}' for s in group_scores]}")
                # print(f"      Mean: {mean_score:.3f}, Std: {std_score:.3f}")
                for i, score in enumerate(group_scores):
                    advantage = (score - mean_score) / (std_score + 1e-6)
                    # print(f"      Response {i}: {score:.3f} → advantage={advantage:.3f}")
            else:
                pass
                # print(f"   Group {idx}: single response, advantage=0.0")

        # ===== Difficulty Calibration Integration =====
        # Extract sample IDs and update difficulty calibration
        sample_ids = []
        target_difficulties = []
        accuracy_scores = torch.zeros(total_completions, device=device)

        # GRPO structure: inputs (original samples) -> completions (num_generations per sample)
        # Each input sample generates num_generations completions
        # print(f"   🔍 GRPO structure: {len(inputs)} inputs → {len(completion_texts)} completions")

        for i, text in enumerate(completion_texts):
            # Calculate which input sample this completion belongs to
            input_idx = i // self.num_generations

            if input_idx < len(inputs):
                x = inputs[input_idx]

                # Extract sample ID from input - id is a top-level field in the dataset
                sample_id = x.get('id')
                if not sample_id:
                    raise ValueError(f"Sample missing required 'id' field: {x}")
                sample_ids.append(sample_id)

                # Extract predicted difficulty from response
                predicted_difficulty = extract_predicted_difficulty(text)

                # Get calibrated difficulty if calibrator is enabled
                if self.difficulty_calibrator:
                    calibrated_difficulty = self.difficulty_calibrator.get_difficulty(sample_ids[i])
                    target_difficulties.append(calibrated_difficulty)
                else:
                    # Fallback: extract from response or default
                    if text.startswith('[') and ']' in text[:10]:
                        diff_end = text.find(']')
                        difficulty = text[1:diff_end].lower()
                        target_difficulties.append(difficulty if difficulty in ['easy', 'hard'] else 'hard')
                    else:
                        target_difficulties.append('hard')  # Default to hard if no tag

                # Extract accuracy score - we need to compute it directly from the data
                if 'solution' in x:
                    # Calculate accuracy by comparing completion with ground truth
                    ground_truth = x['solution']
                    completion = text

                    # Use the same accuracy calculation as the reward function
                    from .open_vocab_reward import calculate_accuracy_reward_standalone
                    accuracy_score = calculate_accuracy_reward_standalone(completion, ground_truth)
                    accuracy_scores[i] = accuracy_score
                    # print(f"   📊 Completion {i}: accuracy={accuracy_score:.3f} (vs ground_truth)")
                else:
                    accuracy_scores[i] = 0.0
                    # print(f"   ❌ Completion {i}: No ground truth available")
            else:
                # This should not happen if data is properly formatted
                raise ValueError(f"Input index {input_idx} out of range for {len(inputs)} inputs")

        # Update difficulty calibrator with current accuracy
        if self.difficulty_calibrator:
            # print(f"\n🔧 [DIFFICULTY CALIBRATOR] Step {self.state.global_step}")
            # print(f"   📋 Processing {len(sample_ids)} samples from GRPO group")
            # print(f"   🆔 Sample IDs: {sample_ids}")
            # print(f"   📊 Accuracy scores: {[f'{acc:.3f}' for acc in accuracy_scores.cpu().tolist()]}")

            # Update accuracy tracking
            self.difficulty_calibrator.update_accuracy(sample_ids, accuracy_scores.cpu().tolist())

            # Check if we should update difficulties
            should_update = self.difficulty_calibrator.should_update_difficulties()
            # print(f"   🔄 Should update difficulties: {should_update}")
            # print(f"   📈 Current step count: {self.difficulty_calibrator.step_count}")
            # print(f"   ⏰ Update frequency: {self.difficulty_calibrator.update_frequency}")

            # Update difficulty labels if needed
            updated_difficulties = self.difficulty_calibrator.update_difficulties()

            # Add debug information every few steps
            if self.state.global_step % 10 == 0:
                self.difficulty_calibrator.debug_sample_status(sample_ids)

            if updated_difficulties:
                # print(f"\n🎯 [APPLYING UPDATES] {len(updated_difficulties)} difficulty changes")
                # Update target_difficulties with newly calibrated values
                for i, sample_id in enumerate(sample_ids):
                    if sample_id in updated_difficulties:
                        old_diff = target_difficulties[i]
                        target_difficulties[i] = updated_difficulties[sample_id]
                        # print(f"   ✅ Sample {sample_id}: {old_diff} → {updated_difficulties[sample_id]}")
            else:
                pass
                # print(f"\n➡️  [NO UPDATES] No difficulty changes applied this round")
        else:
            pass
            # print(f"\n⚠️  [DIFFICULTY CALIBRATOR] Not enabled or not initialized")

        # All additional advantages removed - only using GRPO advantage to avoid duplication
        # print(f"\n🔍 [ADVANTAGES] Only using GRPO advantage (format/length/calibration removed)")

        # Only use GRPO advantages - all other advantages removed to avoid duplication
        advantages = grpo_advantages

        # Debug final advantage combination
        # print(f"\n🔍 [FINAL ADVANTAGE DEBUG]")
        grpo_sum = grpo_advantages.sum(dim=1)

        # print(f"   GRPO advantages (only): {[f'{x:.3f}' for x in grpo_sum.cpu().tolist()]}")
        # print(f"   Note: Format, length, and calibration advantages removed to avoid duplication with main reward function")

        # # Debug the underlying reward scores that led to these advantages
        # print(f"\n🔍 [GRPO ADVANTAGE ANALYSIS]")
        # print(f"   Original reward scores: {[f'{r:.3f}' for r in rewards.cpu().tolist()]}")
        # print(f"   Reward mean: {rewards.mean().item():.3f}")
        # print(f"   Reward std: {rewards.std().item():.3f}")
        # print(f"   Reward min/max: {rewards.min().item():.3f} / {rewards.max().item():.3f}")

        # # Check if there are extreme values
        # if rewards.std().item() < 1e-3:
        #     print(f"   ⚠️  WARNING: Very small reward std ({rewards.std().item():.6f}) may cause advantage explosion!")
        # if grpo_sum.abs().max().item() > 10:
        #     print(f"   ⚠️  WARNING: Extremely high advantage values detected! Max: {grpo_sum.abs().max().item():.3f}")

        # Convert to per-sample advantages for loss computation
        advantages = advantages.sum(dim=1)  # Sum over sequence length

        # print(f"   Combined advantages: {[f'{x:.3f}' for x in advantages.cpu().tolist()]}")
        # print(f"   Average advantage: {advantages.mean().item():.3f}")
        # print(f"   Advantage std: {advantages.std().item():.3f}")

        # ===== AdaCtrl Enhanced Loss Computation =====
        # Update adaptive KL controller if enabled
        current_kl = per_token_kl.mean().item()
        if self.kl_controller is not None:
            self.kl_controller.update(current_kl, n_steps=1)
            adaptive_beta = self.kl_controller.value
        else:
            adaptive_beta = self.beta

        # Debug loss calculation components
        # print(f"\n🔍 [LOSS CALCULATION DEBUG]")
        # print(f"   Advantages shape: {advantages.shape}, mean: {advantages.mean().item():.6f}, std: {advantages.std().item():.6f}")
        # print(f"   Advantages range: [{advantages.min().item():.6f}, {advantages.max().item():.6f}]")
        # print(f"   per_token_logps shape: {per_token_logps.shape}, mean: {per_token_logps.mean().item():.6f}")
        # print(f"   per_token_kl shape: {per_token_kl.shape}, mean: {per_token_kl.mean().item():.6f}")
        # print(f"   adaptive_beta: {adaptive_beta:.6f}")

        # # GRPO: current policy vs reference policy (not self vs self.detach()!)
        logp_ratio = per_token_logps - ref_per_token_logps.detach()
        # print(f"   logp_ratio mean: {logp_ratio.mean().item():.6f}, std: {logp_ratio.std().item():.6f}")

        importance_weights = torch.exp(logp_ratio)
        # print(f"   importance_weights mean: {importance_weights.mean().item():.6f}, std: {importance_weights.std().item():.6f}")
        # print(f"   importance_weights range: [{importance_weights.min().item():.6f}, {importance_weights.max().item():.6f}]")

        per_token_loss = importance_weights * advantages.unsqueeze(1)
        # print(f"   per_token_loss (before KL) mean: {per_token_loss.mean().item():.6f}")

        kl_penalty = adaptive_beta * per_token_kl
        # print(f"   kl_penalty mean: {kl_penalty.mean().item():.6f}")

        per_token_loss = -(per_token_loss - kl_penalty)
        # print(f"   per_token_loss (after KL) mean: {per_token_loss.mean().item():.6f}")

        # # Calculate loss per sample
        loss_per_sample = (per_token_loss * completion_mask).sum(dim=1) / completion_mask.sum(dim=1)
        # print(f"   loss_per_sample: {[f'{x:.6f}' for x in loss_per_sample.cpu().tolist()]}")

        loss = loss_per_sample.mean()
        # print(f"   final_loss: {loss.item():.6f}")

        # # Check for potential numerical issues
        # if torch.isnan(loss) or torch.isinf(loss):
        #     print(f"   ⚠️  WARNING: Loss is NaN or Inf!")
        # if abs(loss.item()) < 1e-6:
        #     print(f"   ⚠️  WARNING: Loss is extremely small ({loss.item():.6f})!")
        # if abs(loss.item()) > 100:
        #     print(f"   ⚠️  WARNING: Loss is extremely large ({loss.item():.6f})!")

        # ===== Enhanced Metrics Logging =====
        completion_length = self.accelerator.gather_for_metrics(completion_mask.sum(1)).float().mean().item()
        self._metrics["completion_length"].append(completion_length)

        # Debug learning rate
        current_lr = self.optimizer.param_groups[0]['lr'] if self.optimizer else 0.0
        # print(f"\n🔍 [LEARNING RATE DEBUG]")
        # print(f"   Current LR from optimizer: {current_lr:.8f}")
        # print(f"   Training step: {self.state.global_step}")
        # print(f"   Total steps: {self.state.max_steps}")
        # print(f"   Args learning rate: {self.args.learning_rate}")
        # print(f"   Args warmup steps: {getattr(self.args, 'warmup_steps', 'Not set')}")
        # print(f"   Args warmup ratio: {getattr(self.args, 'warmup_ratio', 'Not set')}")
        # print(f"   Args lr_scheduler_type: {getattr(self.args, 'lr_scheduler_type', 'Not set')}")
        # if hasattr(self, 'lr_scheduler') and self.lr_scheduler:
        #     print(f"   LR scheduler type: {type(self.lr_scheduler).__name__}")
        #     if hasattr(self.lr_scheduler, 'get_last_lr'):
        #         print(f"   LR from scheduler: {self.lr_scheduler.get_last_lr()}")
        # else:
        #     print(f"   No LR scheduler found")

        # Log easy-hard GRPO specific metrics
        if self.enable_easy_hard_grpo:
            # Log advantage components (only GRPO advantage now)
            grpo_adv_mean = grpo_advantages.sum(dim=1).mean().item()

            self._metrics["grpo_advantage"].append(grpo_adv_mean)
            # Note: format_advantage and length_advantage removed to avoid duplication

            # Log KL metrics
            self._metrics["kl_divergence"].append(current_kl)
            if self.kl_controller is not None:
                self._metrics["adaptive_beta"].append(adaptive_beta)

            # Log difficulty distribution
            easy_count = sum(1 for diff in target_difficulties if diff == 'easy')
            hard_count = len(target_difficulties) - easy_count
            self._metrics["easy_ratio"].append(easy_count / len(target_difficulties) if target_difficulties else 0)

            # Log accuracy metrics
            accuracy_mean = accuracy_scores.mean().item()
            self._metrics["accuracy"].append(accuracy_mean)

        reward_per_func = self.accelerator.gather_for_metrics(rewards_per_func).mean(0)
        for i, reward_func in enumerate(self.reward_funcs):
            if isinstance(reward_func, PreTrainedModel):
                reward_func_name = reward_func.config._name_or_path.split("/")[-1]
            elif hasattr(reward_func, '__name__'):
                reward_func_name = reward_func.__name__
            else:
                reward_func_name = f"reward_func_{i}"
            self._metrics[f"rewards/{reward_func_name}"].append(reward_per_func[i].item())

        self._metrics["reward"].append(self.accelerator.gather_for_metrics(rewards).mean().item())

        # Compute reward std from grouped rewards
        grouped_rewards = rewards.view(-1, self.num_generations)
        reward_std = grouped_rewards.std(dim=1).mean()
        self._metrics["reward_std"].append(self.accelerator.gather_for_metrics(reward_std).mean().item())

        mean_kl = ((per_token_kl * completion_mask).sum(dim=1) / completion_mask.sum(dim=1)).mean()
        self._metrics["kl"].append(self.accelerator.gather_for_metrics(mean_kl).mean().item())

        return loss

    def prediction_step(self, model, inputs, prediction_loss_only: bool, ignore_keys=None):
        """
        Custom prediction step for GRPO evaluation.
        Since GRPO uses a different data format (list instead of dict), we need to handle this specially.
        """
        # For evaluation, we'll compute a simple reward-based metric
        # Convert inputs to the expected format for compute_loss
        if isinstance(inputs, list):
            # This is the GRPO format, compute loss normally
            with torch.no_grad():
                loss = self.compute_loss(model, inputs, return_outputs=False)

            # Return loss, logits=None, labels=None (standard format for Trainer)
            return (loss, None, None)
        else:
            # Fallback to standard prediction step if inputs is a dict
            return super().prediction_step(model, inputs, prediction_loss_only, ignore_keys)

    def evaluate(self, eval_dataset=None, ignore_keys=None, metric_key_prefix: str = "eval"):
        """
        Custom evaluate method for GRPO that computes full reward-based metrics.
        This will generate completions and compute rewards just like in training.
        """
        eval_dataloader = self.get_eval_dataloader(eval_dataset)

        # Temporarily store metrics for evaluation
        eval_metrics = defaultdict(list)
        eval_losses = []

        model = self._wrap_model(self.model, training=False, dataloader=eval_dataloader)
        model.eval()

        for step, inputs in enumerate(eval_dataloader):
            with torch.no_grad():
                # Compute full GRPO evaluation with generation and rewards
                loss, rewards = self._compute_eval_grpo_loss(model, inputs)
                eval_losses.append(loss.item())

                # Collect the metrics that were logged during compute_loss
                for key, values in self._metrics.items():
                    eval_metrics[key].extend(values)

                # Clear metrics after collecting them
                self._metrics.clear()

        # Compute average metrics
        metrics = {}
        metrics[f"{metric_key_prefix}_loss"] = sum(eval_losses) / len(eval_losses)

        for key, values in eval_metrics.items():
            if values:  # Only add if we have values
                metrics[f"{metric_key_prefix}_{key}"] = sum(values) / len(values)

        self.log(metrics)
        self.control = self.callback_handler.on_evaluate(self.args, self.state, self.control, metrics)

        return metrics

    def _compute_eval_grpo_loss(self, model, inputs):
        """
        Full GRPO evaluation with generation and reward computation.
        This is similar to compute_loss but for evaluation only.
        Now that we've set per_device_eval_batch_size=1, inputs should contain only 1 sample.
        """
        prompts = []
        bert_prompts = []
        for x in inputs:
            prompt = x["prompt"]

            # Extract text content for BERT processing (new format)
            content_text = prompt[0]['content']
            # Remove the <video> and <audio> tags for BERT processing
            bert_text = content_text.replace('<video>\n<audio>\n', '')
            bert_prompts.append(bert_text)

            # Update the prompt content to include the multimodal tokens
            # Remove original tokens and replace with wrapper tokens
            content_text_clean = content_text.replace('<video>\n<audio>\n', '')
            prompt[0]['content'] = '<vi_start><video><vi_end>\n<au_start><audio><au_end>\n' + content_text_clean
            prompts.append(prompt)

        bert_prompts = self.bert_tokenizer(bert_prompts, return_tensors='pt', padding=True, truncation=True, add_special_tokens=True)

        prompts_text = []
        for example in inputs:
            prompt_text = maybe_apply_chat_template(example, self.processing_class)["prompt"]
            prompts_text.append(prompt_text)

        # Use the tokenizer attribute if available, otherwise use processing_class directly
        tokenizer_to_use = getattr(self.processing_class, 'tokenizer', self.processing_class)
        input_ids = [tokenizer_multimodal_token(prompts_text_, tokenizer_to_use, '<video>', return_tensors='pt') for prompts_text_ in prompts_text]
        input_ids = torch.cat(input_ids, dim=0).unsqueeze(0)
        video = []
        audios = []
        for i, prompt in enumerate(prompts):
            # Get video file from the corresponding input data
            video_file = inputs[i]["video"]
            video_ids = process_video_fixed(video_file, self.visual_processor, aspect_ratio="pad", num_frames=8)
            video.append(video_ids)

            audio, audio_sample_rate = process_audio(video_file)
            # Use HumanOmni audio processor (function)
            audio_processed = self.audio_processor(audio)
            # HumanOmni audio processor might return a tuple, extract tensor
            if isinstance(audio_processed, tuple):
                audio = audio_processed[0]  # Take the first element if it's a tuple
            else:
                audio = audio_processed
            audios.append(audio)
        video = torch.cat(video, dim=0).unsqueeze(0)
        audios = torch.cat(audios, dim=0).unsqueeze(0)

        attention_masks = input_ids.ne(self.processing_class.pad_token_id)
        prompt_inputs = {}
        prompt_inputs['inputs'] = input_ids
        prompt_inputs['images'] = video
        prompt_inputs['attention_mask'] = attention_masks
        prompt_inputs['prompts'] = bert_prompts
        prompt_inputs['audios'] = audios

        prompt_inputs = super()._prepare_inputs(prompt_inputs)

        prompt_ids, prompt_mask = prompt_inputs["inputs"], prompt_inputs["attention_mask"]

        if self.max_prompt_length is not None:
            prompt_ids = prompt_ids[:, -self.max_prompt_length :]
            prompt_mask = prompt_mask[:, -self.max_prompt_length :]

        # Generate completions - but use a simpler generation config for evaluation
        eval_generation_config = GenerationConfig(
            max_new_tokens=self.max_completion_length,
            do_sample=True,
            temperature=1.0,
            top_p=0.9,  # Nucleus sampling
            num_return_sequences=1,  # Use only 1 generation for evaluation to save time
            pad_token_id=self.processing_class.pad_token_id,
        )

        # For evaluation, we need to handle the model wrapping differently
        # to avoid the optimizer attribute error
        try:
            # Try to generate without unwrapping first
            prompt_completion_ids = model.generate(**prompt_inputs, generation_config=eval_generation_config)
        except AttributeError:
            # If that fails, use the unwrapped model but handle the optimizer issue
            # by temporarily adding the optimizer attribute
            if not hasattr(model, 'optimizer'):
                model.optimizer = None
            try:
                with unwrap_model_for_generation(model, self.accelerator) as unwrapped_model:
                    prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=eval_generation_config)
            finally:
                # Clean up the temporary attribute
                if hasattr(model, 'optimizer') and model.optimizer is None:
                    delattr(model, 'optimizer')

        prompt_length = prompt_ids.size(1)
        answer_length = prompt_completion_ids.size(1)
        completion_ids = prompt_completion_ids

        # Decode the generated completions
        completions = self.processing_class.batch_decode(completion_ids, skip_special_tokens=True)
        if is_conversational(inputs[0]):
            completions = [[{"role": "assistant", "content": completion}] for completion in completions]

        # Compute the rewards using the same reward functions as training
        device = self.accelerator.device
        rewards_per_func = torch.zeros(len(prompts), len(self.reward_funcs), device=device)
        for i, (reward_func, reward_processing_class) in enumerate(
            zip(self.reward_funcs, self.reward_processing_classes)
        ):
            if isinstance(reward_func, PreTrainedModel):
                if is_conversational(inputs[0]):
                    messages = [{"messages": p + c} for p, c in zip(prompts, completions)]
                    texts = [apply_chat_template(x, reward_processing_class)["text"] for x in messages]
                else:
                    texts = [p + c for p, c in zip(prompts, completions)]
                reward_inputs = reward_processing_class(
                    texts, return_tensors="pt", padding=True, padding_side="right", add_special_tokens=False
                )
                reward_inputs = super()._prepare_inputs(reward_inputs)
                with torch.inference_mode():
                    rewards_per_func[:, i] = reward_func(**reward_inputs).logits[:, 0]
            else:
                # Handle custom reward functions
                reward_kwargs = {key: [] for key in inputs[0].keys() if key not in ["prompt", "completion"]}
                for key in reward_kwargs:
                    for example in inputs:
                        reward_kwargs[key].append(example[key])
                output_reward_func = reward_func(prompts=prompts, completions=completions, **reward_kwargs)
                rewards_per_func[:, i] = torch.tensor(output_reward_func, dtype=torch.float32, device=device)

        # Sum the rewards from all reward functions
        rewards = rewards_per_func.sum(dim=1)

        # Log the reward metric for evaluation
        avg_reward = rewards.mean().item()
        self._metrics["reward"].append(avg_reward)

        # For evaluation, we'll return a simple loss (negative reward)
        loss = -rewards.mean()

        return loss, rewards

    def log(self, logs: dict[str, float], start_time: Optional[float] = None) -> None:
        metrics = {key: sum(val) / len(val) for key, val in self._metrics.items()}  # average the metrics
        logs = {**logs, **metrics}
        if version.parse(transformers.__version__) >= version.parse("4.47.0.dev0"):
            super().log(logs, start_time)
        else:  # transformers<=4.46
            super().log(logs)
        self._metrics.clear()

    def create_model_card(
        self,
        model_name: Optional[str] = None,
        dataset_name: Optional[str] = None,
        tags: Union[str, list[str], None] = None,
    ):
        """
        Creates a draft of a model card using the information available to the `Trainer`.

        Args:
            model_name (`str` or `None`, *optional*, defaults to `None`):
                Name of the model.
            dataset_name (`str` or `None`, *optional*, defaults to `None`):
                Name of the dataset used for training.
            tags (`str`, `list[str]` or `None`, *optional*, defaults to `None`):
                Tags to be associated with the model card.
        """
        if not self.is_world_process_zero():
            return

        if hasattr(self.model.config, "_name_or_path") and not os.path.isdir(self.model.config._name_or_path):
            base_model = self.model.config._name_or_path
        else:
            base_model = None

        tags = tags or []
        if isinstance(tags, str):
            tags = [tags]

        if hasattr(self.model.config, "unsloth_version"):
            tags.append("unsloth")

        citation = textwrap.dedent(
            """\
            @article{zhihong2024deepseekmath,
                title        = {{DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models}},
                author       = {Zhihong Shao and Peiyi Wang and Qihao Zhu and Runxin Xu and Junxiao Song and Mingchuan Zhang and Y. K. Li and Y. Wu and Daya Guo},
                year         = 2024,
                eprint       = {arXiv:2402.03300},
            """
        )

        model_card = generate_model_card(
            base_model=base_model,
            model_name=model_name,
            hub_model_id=self.hub_model_id,
            dataset_name=dataset_name,
            tags=tags,
            wandb_url=wandb.run.get_url() if is_wandb_available() and wandb.run is not None else None,
            comet_url=get_comet_experiment_url(),
            trainer_name="GRPO",
            trainer_citation=citation,
            paper_title="DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models",
            paper_id="2402.03300",
        )

        model_card.save(os.path.join(self.args.output_dir, "README.md"))
