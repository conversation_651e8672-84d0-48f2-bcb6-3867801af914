#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Difficulty Estimation Calibration for Easy-Hard GRPO
Implements dynamic difficulty adjustment based on model performance
"""

import torch
import numpy as np
import pandas as pd
from collections import defaultdict, deque
from typing import Dict, List, Any, Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)


class DifficultyCalibrator:
    """
    Difficulty calibrator that dynamically adjusts sample difficulty labels
    based on model performance, following AdaCtrl's approach.
    """
    
    def __init__(self, 
                 diff_threshold: float = 0.7,
                 window_size: int = 100,
                 min_samples: int = 10,
                 update_frequency: int = 50,
                 smoothing_factor: float = 0.1):
        """
        Initialize the difficulty calibrator.
        
        Args:
            diff_threshold: Accuracy threshold for difficulty classification
                          (>= threshold: easy, < threshold: hard)
            window_size: Size of sliding window for accuracy calculation
            min_samples: Minimum samples needed before updating difficulty
            update_frequency: How often to update difficulty labels (in steps)
            smoothing_factor: Exponential smoothing factor for accuracy updates
        """
        self.diff_threshold = diff_threshold
        self.window_size = window_size
        self.min_samples = min_samples
        self.update_frequency = update_frequency
        self.smoothing_factor = smoothing_factor
        
        # Track accuracy for each sample ID
        self.id2acc = defaultdict(lambda: deque(maxlen=window_size))
        self.id2diff = {}  # Current difficulty labels
        self.id2smooth_acc = defaultdict(float)  # Smoothed accuracy

        # Global difficulty counters
        self.total_easy_samples = 0
        self.total_hard_samples = 0

        # Statistics
        self.step_count = 0
        self.total_updates = 0
        self.difficulty_changes = 0
        
        logger.info(f"DifficultyCalibrator initialized with threshold={diff_threshold}")
    
    def load_initial_difficulties(self, data_path: str) -> Dict[str, str]:
        """
        Load initial difficulty labels from CSV data.
        
        Args:
            data_path: Path to CSV file with columns: id, is_correct
            
        Returns:
            Dictionary mapping sample IDs to initial difficulty labels
        """
        try:
            df = pd.read_csv(data_path)
            logger.info(f"Loaded {len(df)} samples from {data_path}")
            
            initial_difficulties = {}
            for _, row in df.iterrows():
                sample_id = str(row['id'])
                is_correct = row['is_correct']
                
                # Initial difficulty based on ground truth correctness
                if is_correct:
                    initial_difficulties[sample_id] = 'easy'
                else:
                    initial_difficulties[sample_id] = 'hard'
            
            self.id2diff.update(initial_difficulties)

            easy_count = sum(1 for d in initial_difficulties.values() if d == 'easy')
            hard_count = len(initial_difficulties) - easy_count

            # Initialize global counters
            self.total_easy_samples = easy_count
            self.total_hard_samples = hard_count

            logger.info(f"Initial difficulty distribution: {easy_count} easy, {hard_count} hard")
            return initial_difficulties
            
        except Exception as e:
            logger.error(f"Failed to load initial difficulties: {e}")
            return {}
    
    def update_accuracy(self, sample_ids: List[str], accuracies: List[float]):
        """
        Update accuracy tracking for given samples.

        Args:
            sample_ids: List of sample IDs
            accuracies: List of accuracy scores (0.0 to 1.0)
        """
        for sample_id, accuracy in zip(sample_ids, accuracies):
            # Add to sliding window
            self.id2acc[sample_id].append(accuracy)
            window_size = len(self.id2acc[sample_id])

            # Update smoothed accuracy
            if sample_id not in self.id2smooth_acc:
                self.id2smooth_acc[sample_id] = accuracy
            else:
                self.id2smooth_acc[sample_id] = (
                    (1 - self.smoothing_factor) * self.id2smooth_acc[sample_id] +
                    self.smoothing_factor * accuracy
                )
        
        self.step_count += 1
    
    def get_current_accuracy(self, sample_id: str) -> Optional[float]:
        """
        Get current accuracy for a sample.
        
        Args:
            sample_id: Sample ID
            
        Returns:
            Current accuracy or None if no data
        """
        if sample_id in self.id2acc and len(self.id2acc[sample_id]) > 0:
            return np.mean(list(self.id2acc[sample_id]))
        return None
    
    def should_update_difficulties(self) -> bool:
        """
        Check if it's time to update difficulty labels.
        For GRPO groups, we always check after each group - frequency parameter is ignored.

        Returns:
            True (always update after each GRPO group)
        """
        # For GRPO, we always check after each group regardless of frequency
        # The update decision is based on group performance, not step count
        return True
    
    def update_difficulties(self) -> Dict[str, str]:
        """
        Update difficulty labels based on current accuracy.
        Only allows hard → easy transitions based on GRPO group performance.

        Returns:
            Dictionary of updated difficulty labels
        """
        if not self.should_update_difficulties():
            return {}

        updated_difficulties = {}
        hard_to_easy_count = 0

        # Only process samples that have accuracy data in current batch
        for sample_id in self.id2acc:
            sample_count = len(self.id2acc[sample_id])

            # For GRPO groups, we don't need minimum samples - process immediately
            if sample_count == 0:
                continue

            # Calculate current accuracy
            current_acc = np.mean(list(self.id2acc[sample_id]))
            old_difficulty = self.id2diff.get(sample_id, 'hard')

            # Only allow hard → easy transitions (not easy → hard)
            if old_difficulty == 'hard' and current_acc >= self.diff_threshold:
                new_difficulty = 'easy'
                self.id2diff[sample_id] = new_difficulty
                updated_difficulties[sample_id] = new_difficulty
                hard_to_easy_count += 1

                # Update global counters
                self.total_hard_samples -= 1
                self.total_easy_samples += 1

        # Summary statistics using global counters
        print(f"\n📊 [UPDATE SUMMARY]")
        print(f"   🔄 Hard → Easy: {hard_to_easy_count}")
        print(f"   ✅ Already Easy: {self.total_easy_samples}")
        print(f"   ❌ Still Hard: {self.total_hard_samples}")
        print(f"   📈 Total changes: {hard_to_easy_count}")

        if hard_to_easy_count > 0:
            self.difficulty_changes += hard_to_easy_count
            self.total_updates += 1
            logger.info(f"Updated {hard_to_easy_count} difficulty labels at step {self.step_count}")

        return updated_difficulties
    
    def get_difficulty(self, sample_id: str) -> str:
        """
        Get current difficulty label for a sample.
        
        Args:
            sample_id: Sample ID
            
        Returns:
            Difficulty label ('easy' or 'hard')
        """
        return self.id2diff.get(sample_id, 'hard')  # Default to hard
    
    def get_difficulty_distribution(self) -> Dict[str, int]:
        """
        Get current difficulty distribution using global counters.

        Returns:
            Dictionary with counts of easy and hard samples
        """
        return {
            'easy': self.total_easy_samples,
            'hard': self.total_hard_samples
        }

    def debug_sample_status(self, sample_ids: Optional[List[str]] = None) -> None:
        """
        Debug method to print global difficulty status information.

        Args:
            sample_ids: Optional list of specific sample IDs to debug (for current batch info)
        """
        current_batch_size = len(sample_ids) if sample_ids else len(self.id2acc)

        print(f"\n🔍 [DEBUG DIFFICULTY STATUS]")
        print(f"   📊 Current batch: {current_batch_size} samples")
        print(f"   🏷️  Total tracked samples: {len(self.id2diff)} samples")
        print(f"   ✅ Easy samples: {self.total_easy_samples}")
        print(f"   ❌ Hard samples: {self.total_hard_samples}")
        print(f"   📈 Easy ratio: {self.total_easy_samples / (self.total_easy_samples + self.total_hard_samples):.2%}")
        print(f"   � Total difficulty changes: {self.difficulty_changes}")
        print(f"   📋 Update count: {self.total_updates}")
        print(f"   🎯 Threshold: {self.diff_threshold}")
    
    def compute_calibration_reward(self, 
                                 sample_ids: List[str], 
                                 predicted_difficulties: List[str],
                                 reward_weight: float = 0.1) -> torch.Tensor:
        """
        Compute calibration reward based on difficulty prediction accuracy.
        
        Args:
            sample_ids: List of sample IDs
            predicted_difficulties: List of predicted difficulty labels
            reward_weight: Weight for calibration reward
            
        Returns:
            Tensor of calibration rewards
        """
        rewards = []
        
        for sample_id, pred_diff in zip(sample_ids, predicted_difficulties):
            true_diff = self.get_difficulty(sample_id)
            
            # Reward correct difficulty prediction
            if pred_diff == true_diff:
                reward = reward_weight
            else:
                reward = -reward_weight
            
            rewards.append(reward)
        
        return torch.tensor(rewards, dtype=torch.float32)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get calibration statistics.
        
        Returns:
            Dictionary with calibration statistics
        """
        distribution = self.get_difficulty_distribution()
        total_samples = sum(distribution.values())
        
        # Calculate average accuracy by difficulty
        easy_accs = []
        hard_accs = []
        
        for sample_id, difficulty in self.id2diff.items():
            if sample_id in self.id2smooth_acc:
                acc = self.id2smooth_acc[sample_id]
                if difficulty == 'easy':
                    easy_accs.append(acc)
                else:
                    hard_accs.append(acc)
        
        stats = {
            'total_samples': total_samples,
            'easy_count': distribution['easy'],
            'hard_count': distribution['hard'],
            'easy_ratio': distribution['easy'] / total_samples if total_samples > 0 else 0,
            'total_updates': self.total_updates,
            'difficulty_changes': self.difficulty_changes,
            'step_count': self.step_count,
            'avg_easy_accuracy': np.mean(easy_accs) if easy_accs else 0,
            'avg_hard_accuracy': np.mean(hard_accs) if hard_accs else 0,
            'threshold': self.diff_threshold
        }
        
        return stats
    
    def reset_statistics(self):
        """Reset calibration statistics."""
        self.step_count = 0
        self.total_updates = 0
        self.difficulty_changes = 0
        logger.info("Calibration statistics reset")
    
    def save_state(self, filepath: str):
        """
        Save calibrator state to file.
        
        Args:
            filepath: Path to save state
        """
        import pickle
        
        state = {
            'id2diff': dict(self.id2diff),
            'id2smooth_acc': dict(self.id2smooth_acc),
            'total_easy_samples': self.total_easy_samples,
            'total_hard_samples': self.total_hard_samples,
            'step_count': self.step_count,
            'total_updates': self.total_updates,
            'difficulty_changes': self.difficulty_changes,
            'diff_threshold': self.diff_threshold
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
        
        logger.info(f"Calibrator state saved to {filepath}")
    
    def load_state(self, filepath: str):
        """
        Load calibrator state from file.
        
        Args:
            filepath: Path to load state from
        """
        import pickle
        
        try:
            with open(filepath, 'rb') as f:
                state = pickle.load(f)
            
            self.id2diff = defaultdict(str, state['id2diff'])
            self.id2smooth_acc = defaultdict(float, state['id2smooth_acc'])
            self.total_easy_samples = state.get('total_easy_samples', 0)
            self.total_hard_samples = state.get('total_hard_samples', 0)
            self.step_count = state['step_count']
            self.total_updates = state['total_updates']
            self.difficulty_changes = state['difficulty_changes']
            self.diff_threshold = state['diff_threshold']
            
            logger.info(f"Calibrator state loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to load calibrator state: {e}")



def extract_predicted_difficulty(response_text: str) -> str:
    """
    Extract predicted difficulty from response text.
    
    Args:
        response_text: Model response text
        
    Returns:
        Predicted difficulty ('easy' or 'hard')
    """
    import re
    
    # Look for [easy] or [hard] tags
    pattern = r'\[(easy|hard)\]'
    match = re.search(pattern, response_text.lower())
    
    if match:
        return match.group(1)
    
    # Default to hard if no tag found
    return 'hard'
