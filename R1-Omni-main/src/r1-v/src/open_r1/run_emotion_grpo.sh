#!/bin/bash

# Enhanced Easy-Hard GRPO Training Script for Emotion Recognition
# This script launches training with the integrated AdaCtrl difficulty calibration
# and the new Difficulty-aware Length Reward function
#
# 要从checkpoint恢复训练：取消注释第74行并设置checkpoint路径
#
# WANDB配置：
# - 使用离线模式 (WANDB_MODE=offline)
# - 项目名称：emotion-grpo
# - 日志保存在本地，可后续上传到wandb

set -e

# Activate conda environment
echo "🔧 Activating conda environment..."
source activate /data/wuyang/conda_envs/r1-omni
echo "✅ Conda environment activated: $(which python)"

# Configuration
MODEL_NAME="/data/wuyang/PLM/EMER-SFT-0.5B"  # You can change this to your preferred model
OUTPUT_DIR="./emotion_grpo_output_full"
DATASET_PATH="emotion_grpo_dataset_fixed.json"
CSV_PATH="/data/wuyang/MERGE_GRPO/sample_results.csv"

# Training parameters
BATCH_SIZE=1
EVAL_BATCH_SIZE=1
LEARNING_RATE=5e-6
NUM_EPOCHS=2
GRADIENT_ACCUMULATION_STEPS=2

# Enhanced Reward Function parameters (always enabled)
ENHANCED_FORMAT_WEIGHT=0.5
ENHANCED_ACCURACY_WEIGHT=0.5  # Fixed: weights should sum to 1.0 (0.2 + 0.6 + 0.2 = 1.0)
ENHANCED_LENGTH_WEIGHT=0
# Note: Difficulty labels are now taken directly from dataset, no threshold needed

# Easy-Hard GRPO parameters
ENABLE_DIFFICULTY_CALIBRATION=true
DIFF_THRESHOLD=0.5  # Threshold for difficulty calibrator (accuracy-based reclassification)
# Note: DIFFICULTY_UPDATE_FREQ removed - updates happen after each GRPO group

echo "🎯 Starting Enhanced Emotion Recognition with Easy-Hard GRPO"
echo "============================================================"
echo "Model: $MODEL_NAME"
echo "Dataset: $DATASET_PATH"
echo "Output: $OUTPUT_DIR"
echo "Enhanced Reward: Always enabled"
echo "Reward Weights: Format=$ENHANCED_FORMAT_WEIGHT, Accuracy=$ENHANCED_ACCURACY_WEIGHT, Length=$ENHANCED_LENGTH_WEIGHT"
echo "Difficulty Source: Dataset labels (updated by calibrator)"
echo "Difficulty Calibration: $ENABLE_DIFFICULTY_CALIBRATION (threshold=$DIFF_THRESHOLD)"
echo "============================================================"

# Set environment variables
export PYTHONPATH="/data/wuyang/MERGE_GRPO/R1-Omni-main:/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1:/data/wuyang/AffectGPT-master/OV-MER:$PYTHONPATH"

# Check if dataset exists, if not create it
if [ ! -f "$DATASET_PATH" ]; then
    echo "📊 Dataset not found, creating from CSV..."
    python data/prepare_emotion_dataset.py \
        --csv_path "$CSV_PATH" \
        --output_path "$DATASET_PATH"
    echo "✅ Dataset created successfully"
fi

# Set CUDA device visibility to use only 2 GPUs (like original script)
export CUDA_VISIBLE_DEVICES="0,1,2,3"

# 要从checkpoint恢复训练，取消注释下面这行并设置正确的路径：
# RESUME_ARG="--resume_from_checkpoint ./emotion_grpo_output/checkpoint-800"
RESUME_ARG=""

# Run training with torchrun for distributed training (like original script)
echo "🚀 Starting training with distributed setup..."

WANDB_MODE=offline torchrun --nproc_per_node="1" \
    --nnodes="1" \
    --node_rank="0" \
    --master_addr="127.0.0.1" \
    --master_port="12347" \
    train_emotion_easy_hard_grpo.py \
    --model_name_or_path "$MODEL_NAME" \
    --dataset_name "$DATASET_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --deepspeed /data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/local_scripts/zero3.json \
    $RESUME_ARG \
    --per_device_train_batch_size $BATCH_SIZE \
    --per_device_eval_batch_size $EVAL_BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --learning_rate $LEARNING_RATE \
    --num_train_epochs $NUM_EPOCHS \
    --enable_easy_hard_grpo true \
    --enable_difficulty_calibration $ENABLE_DIFFICULTY_CALIBRATION \
    --diff_threshold $DIFF_THRESHOLD \
    --difficulty_data_path "$CSV_PATH" \
    --enhanced_format_weight $ENHANCED_FORMAT_WEIGHT \
    --enhanced_accuracy_weight $ENHANCED_ACCURACY_WEIGHT \
    --enhanced_length_weight $ENHANCED_LENGTH_WEIGHT \
    --eval_strategy no \
    --eval_steps 10000 \
    --save_strategy steps \
    --save_steps 200 \
    --load_best_model_at_end false \
    --save_total_limit 5 \
    --warmup_steps 100 \
    --weight_decay 0.01 \
    --max_grad_norm 1.0 \
    --bf16 \
    --report_to wandb \
    --run_name emotion_easy_hard_grpo_$(date +%Y%m%d_%H%M%S) \
    --logging_first_step true \
    --logging_steps 1 \
    --think_answer_format true \
    --difficulty_tag_format true \
    --max_think_length 256 \
    --max_answer_length 32 \
    --reward_functions enhanced_emotion \
    --reward_weights 1.0 \
    --use_ew_evaluation true \
    --adaptive_kl true \
    --init_kl_coef 0.1 \
    --target_kl 0.1 \
    --kl_horizon 10000 \
    --gradient_checkpointing false \
    --attn_implementation flash_attention_2 \
    --max_pixels 401408 \
    --num_generations 6 \
    --max_prompt_length 512 \
    --max_completion_length 512 \
    --dataset_train_split train \
    --dataset_test_split validation

echo "✅ Training completed!"
echo "📊 Results saved to: $OUTPUT_DIR"
echo "📈 Wandb logs: ./wandb/ (offline mode)"
echo "💡 To upload wandb logs later: wandb sync ./wandb/offline-*"

# Optional: Run evaluation on test set
if [ "$1" = "--eval" ]; then
    echo "🔍 Running final evaluation..."
    python evaluate_emotion_model.py \
        --model_path "$OUTPUT_DIR" \
        --dataset_path "$DATASET_PATH" \
        --output_path "$OUTPUT_DIR/final_evaluation.json"
fi

echo "🎉 All done!"
