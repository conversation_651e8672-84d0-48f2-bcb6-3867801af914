#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Evaluation script for emotion recognition model
Focuses on accuracy calculation for model selection
"""

import os
import sys
import json
import torch
import argparse
from datasets import Dataset
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm

# Add project paths
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trainer.open_vocab_reward import OpenVocabRewardFunction
from trainer.adactrl_core_algos import extract_think_answer_content


def load_model_and_tokenizer(model_path: str):
    """
    Load trained model and tokenizer.
    
    Args:
        model_path: Path to trained model
        
    Returns:
        Tuple of (model, tokenizer)
    """
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        trust_remote_code=True,
        padding_side="left"
    )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True
    )
    
    model.eval()
    return model, tokenizer


def generate_response(model, tokenizer, prompt, max_length=512):
    """
    Generate response for a given prompt.
    
    Args:
        model: Trained model
        tokenizer: Tokenizer
        prompt: Input prompt
        max_length: Maximum generation length
        
    Returns:
        Generated response text
    """
    # Format prompt for generation
    if isinstance(prompt, list):
        # Handle conversational format
        prompt_text = ""
        for turn in prompt:
            if turn["role"] == "user":
                content = turn["content"]
                if isinstance(content, list):
                    for item in content:
                        if item["type"] == "text":
                            prompt_text += item["text"]
                else:
                    prompt_text += content
    else:
        prompt_text = prompt
    
    # Tokenize
    inputs = tokenizer(
        prompt_text,
        return_tensors="pt",
        padding=True,
        truncation=True,
        max_length=512
    )
    
    # Move to device
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # Generate
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.pad_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    # Decode response
    response = tokenizer.decode(
        outputs[0][inputs["input_ids"].shape[1]:],
        skip_special_tokens=True
    )
    
    return response.strip()


def evaluate_accuracy(model, tokenizer, eval_dataset, use_ew_evaluation=True):
    """
    Evaluate model accuracy on validation set.
    
    Args:
        model: Trained model
        tokenizer: Tokenizer
        eval_dataset: Evaluation dataset
        use_ew_evaluation: Whether to use EW-based evaluation
        
    Returns:
        Dictionary with evaluation results
    """
    # Initialize reward function for accuracy calculation
    reward_func = OpenVocabRewardFunction(use_ew_evaluation=use_ew_evaluation)
    
    correct_predictions = 0
    total_predictions = 0
    format_correct = 0
    difficulty_correct = 0
    
    results = []
    
    for i, sample in enumerate(tqdm(eval_dataset)):
        try:
            # Generate response
            response = generate_response(model, tokenizer, sample["prompt"])
            
            # Extract answer from response
            think_content, answer_content = extract_think_answer_content(response)
            
            # Check format
            has_correct_format = (think_content is not None and answer_content is not None)
            if has_correct_format:
                format_correct += 1
            
            # Check difficulty prediction
            predicted_difficulty = "easy" if response.startswith("[easy]") else "hard"
            true_difficulty = sample.get("difficulty", "hard")
            if predicted_difficulty == true_difficulty:
                difficulty_correct += 1
            
            # Calculate accuracy
            if answer_content:
                accuracy = reward_func.compute_accuracy_reward(
                    answer_content, 
                    sample["solution"]
                )
                if accuracy > 0.5:  # Consider as correct
                    correct_predictions += 1
            
            total_predictions += 1
            
            # Store result
            result = {
                "sample_id": sample.get("id", f"sample_{i}"),
                "prompt": sample["prompt"],
                "ground_truth": sample["solution"],
                "predicted_response": response,
                "predicted_answer": answer_content,
                "accuracy": accuracy if answer_content else 0.0,
                "format_correct": has_correct_format,
                "difficulty_correct": predicted_difficulty == true_difficulty,
                "true_difficulty": true_difficulty,
                "predicted_difficulty": predicted_difficulty
            }
            results.append(result)
            
        except Exception as e:
            continue
    
    # Calculate final metrics
    overall_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    format_accuracy = format_correct / total_predictions if total_predictions > 0 else 0
    difficulty_accuracy = difficulty_correct / total_predictions if total_predictions > 0 else 0
    
    evaluation_results = {
        "overall_accuracy": overall_accuracy,
        "format_accuracy": format_accuracy,
        "difficulty_accuracy": difficulty_accuracy,
        "correct_predictions": correct_predictions,
        "total_predictions": total_predictions,
        "format_correct": format_correct,
        "difficulty_correct": difficulty_correct,
        "detailed_results": results
    }
    
    return evaluation_results


def main():
    parser = argparse.ArgumentParser(description="Evaluate emotion recognition model")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to trained model")
    parser.add_argument("--dataset_path", type=str, required=True,
                       help="Path to dataset JSON file")
    parser.add_argument("--output_path", type=str, default="evaluation_results.json",
                       help="Output path for evaluation results")
    parser.add_argument("--use_ew_evaluation", action="store_true", default=True,
                       help="Use EW-based evaluation")
    parser.add_argument("--max_samples", type=int, default=None,
                       help="Maximum number of samples to evaluate")
    
    args = parser.parse_args()
    
    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer(args.model_path)

    # Load dataset
    with open(args.dataset_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    eval_dataset = Dataset.from_list(data['validation'])

    if args.max_samples:
        eval_dataset = eval_dataset.select(range(min(args.max_samples, len(eval_dataset))))

    # Run evaluation
    results = evaluate_accuracy(
        model=model,
        tokenizer=tokenizer,
        eval_dataset=eval_dataset,
        use_ew_evaluation=args.use_ew_evaluation
    )

    # Save results
    with open(args.output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    return results['overall_accuracy']


if __name__ == "__main__":
    accuracy = main()
    sys.exit(0)
