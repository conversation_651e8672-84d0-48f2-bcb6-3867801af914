🔧 Activating conda environment...
✅ Conda environment activated: /data/wuyang/conda_envs/r1-omni/bin/python
🎯 Starting Emotion Recognition with Easy-Hard GRPO
==================================================
Model: /data/wuyang/PLM/EMER-SFT-0.5B
Dataset: emotion_grpo_dataset.json
Output: ./emotion_grpo_output
Difficulty Calibration: true
==================================================
🚀 Starting training with distributed setup...
W0728 17:10:29.770561 49159 site-packages/torch/distributed/run.py:793] 
W0728 17:10:29.770561 49159 site-packages/torch/distributed/run.py:793] *****************************************
W0728 17:10:29.770561 49159 site-packages/torch/distributed/run.py:793] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0728 17:10:29.770561 49159 site-packages/torch/distributed/run.py:793] *****************************************
[2025-07-28 17:10:34,302] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-28 17:10:34,975] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
7386
label number: 1255
✅ EW-based open vocabulary evaluation initialized successfully
7386
[2025-07-28 17:10:38,166] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-28 17:10:38,166] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
============================================================
🎯 Emotion Recognition with Easy-Hard GRPO Training
============================================================
Model: /data/wuyang/PLM/EMER-SFT-0.5B
Output directory: ./emotion_grpo_output
Easy-Hard GRPO: True
Difficulty calibration: True
Batch size: 1
Learning rate: 5e-06
Epochs: 3.0
============================================================
label number: 1255
✅ EW-based open vocabulary evaluation initialized successfully
[2025-07-28 17:10:39,041] [INFO] [comm.py:652:init_distributed] cdb=None
Loaded train dataset: 17160 samples
Loaded eval dataset: 4291 samples
Reward functions: ['emotion_accuracy_reward', 'emotion_format_reward']

Distributed training detected - letting trainer handle model loading...
Initializing Easy-Hard GRPO trainer...
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-07-28 17:10:39,102] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
============================================================
🎯 Emotion Recognition with Easy-Hard GRPO Training
============================================================
Model: /data/wuyang/PLM/EMER-SFT-0.5B
Output directory: ./emotion_grpo_output
Easy-Hard GRPO: True
Difficulty calibration: True
Batch size: 1
Learning rate: 5e-06
Epochs: 3.0
============================================================
Loaded train dataset: 17160 samples
Loaded eval dataset: 4291 samples
Reward functions: ['emotion_accuracy_reward', 'emotion_format_reward']

Distributed training detected - letting trainer handle model loading...
Initializing Easy-Hard GRPO trainer...
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-07-28 17:10:40,429] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:42,026] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 624, num_elems = 0.78B
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-28 17:10:43,322] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-28 17:10:43,345] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:43,441] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 832, num_elems = 0.87B
[2025-07-28 17:10:43,734] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:43,745] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:43,948] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 1323, num_elems = 1.51B
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
[2025-07-28 17:10:46,063] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
[2025-07-28 17:10:46,091] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:46,724] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 1947, num_elems = 2.29B
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-28 17:10:48,330] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-28 17:10:48,355] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:48,449] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 2155, num_elems = 2.38B
[2025-07-28 17:10:48,735] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:48,746] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:48,952] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 2646, num_elems = 3.02B
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Detected kernel version 3.10.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
[2025-07-28 17:10:51,426] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:51,445] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-07-28 17:10:51,445] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 2
[2025-07-28 17:10:51,470] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-07-28 17:10:51,474] [INFO] [logging.py:128:log_dist] [Rank 0] Creating ZeRO Offload
[2025-07-28 17:10:51,803] [INFO] [utils.py:781:see_memory_usage] DeepSpeedZeRoOffload initialize [begin]
[2025-07-28 17:10:51,804] [INFO] [utils.py:782:see_memory_usage] MA 2.57 GB         Max_MA 2.6 GB         CA 2.68 GB         Max_CA 3 GB 
[2025-07-28 17:10:51,805] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 15.02 GB, percent = 6.0%
Parameter Offload: Total persistent parameters: 967237 in 759 params
[2025-07-28 17:10:52,151] [INFO] [utils.py:781:see_memory_usage] DeepSpeedZeRoOffload initialize [end]
[2025-07-28 17:10:52,152] [INFO] [utils.py:782:see_memory_usage] MA 2.57 GB         Max_MA 2.57 GB         CA 2.68 GB         Max_CA 3 GB 
[2025-07-28 17:10:52,152] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 15.02 GB, percent = 6.0%
[2025-07-28 17:10:52,156] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-07-28 17:10:52,156] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f6868052890>
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-07-28 17:10:52,157] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   train_batch_size ............. 8
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   world_size ................... 2
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  False
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   zero_config .................. stage=3 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=500000000 overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=DeepSpeedZeroOffloadParamConfig(device='none', nvme_path=None, buffer_count=5, buffer_size=100000000, max_in_cpu=1000000000, pin_memory=True) offload_optimizer=DeepSpeedZeroOffloadOptimizerConfig(device='none', nvme_path=None, buffer_count=4, pin_memory=True, pipeline_read=False, pipeline_write=False, fast_init=False, ratio=1.0) sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=True use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-07-28 17:10:52,158] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 3
[2025-07-28 17:10:52,158] [INFO] [config.py:989:print_user_config]   json = {
    "fp16": {
        "enabled": false, 
        "loss_scale": 0, 
        "loss_scale_window": 1000, 
        "initial_scale_power": 16, 
        "hysteresis": 2, 
        "min_loss_scale": 1
    }, 
    "bf16": {
        "enabled": true
    }, 
    "zero_optimization": {
        "stage": 3, 
        "offload_optimizer": {
            "device": "none", 
            "pin_memory": true
        }, 
        "offload_param": {
            "device": "none", 
            "pin_memory": true
        }, 
        "overlap_comm": true, 
        "contiguous_gradients": true, 
        "sub_group_size": 1.000000e+09, 
        "reduce_bucket_size": "auto", 
        "stage3_prefetch_bucket_size": "auto", 
        "stage3_param_persistence_threshold": "auto", 
        "stage3_max_live_parameters": 1.000000e+09, 
        "stage3_max_reuse_distance": 1.000000e+09, 
        "stage3_gather_16bit_weights_on_model_save": true
    }, 
    "gradient_accumulation_steps": 4, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 8, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "zero_optimization.reduce_bucket_size": 8.028160e+05, 
    "zero_optimization.stage3_param_persistence_threshold": 8.960000e+03, 
    "zero_optimization.stage3_prefetch_bucket_size": 7.225344e+05
}
✓ Difficulty calibrator initialized
  Initial samples: 21451
  Easy: 4643, Hard: 16808
✓ Adaptive KL controller initialized
  Initial KL coefficient: 0.1

🚀 Starting training...
✓ Difficulty calibrator initialized
  Initial samples: 21451
  Easy: 4643, Hard: 16808
✓ Adaptive KL controller initialized
  Initial KL coefficient: 0.1

🚀 Starting training...
Gradient accumulation steps mismatch: GradientAccumulationPlugin has 1, DeepSpeed config has 4. Using DeepSpeed's value.
Parameter Offload: Total persistent parameters: 956485 in 758 params
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  0%|          | 0/6435 [00:00<?, ?it/s]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 2618: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 3927: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 5236: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 6545: cache has only 0 modules

  0%|          | 1/6435 [01:27<157:08:02, 87.92s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 7854: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 9163: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 10472: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 11781: cache has only 0 modules

  0%|          | 2/6435 [02:53<154:52:36, 86.67s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 13090: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 14399: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 15708: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 17017: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  0%|          | 3/6435 [04:18<153:27:18, 85.89s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 18326: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 19635: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 20944: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 22253: cache has only 0 modules

  0%|          | 4/6435 [05:38<148:57:25, 83.38s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 23562: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 24871: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 26180: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 27489: cache has only 0 modules

  0%|          | 5/6435 [06:57<146:24:03, 81.97s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 28798: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 30107: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 31416: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 32725: cache has only 0 modules

  0%|          | 6/6435 [08:19<146:14:18, 81.89s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 34034: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 35343: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 36652: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 37961: cache has only 0 modules

  0%|          | 7/6435 [09:39<145:02:22, 81.23s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 39270: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 40579: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 41888: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 43197: cache has only 0 modules

  0%|          | 8/6435 [11:01<145:18:10, 81.39s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 44506: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 45815: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=100):
   EW-based hits: 24 (24.0%)
   Fallback hits: 2 (2.0%)
   Overall hit rate: 26.0%
   Misses: 74 (74.0%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 47124: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 48433: cache has only 0 modules

  0%|          | 9/6435 [12:22<145:26:08, 81.48s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 49742: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 51051: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=100):
   EW-based hits: 18 (18.0%)
   Fallback hits: 9 (9.0%)
   Overall hit rate: 27.0%
   Misses: 73 (73.0%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 52360: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 53669: cache has only 0 modules

  0%|          | 10/6435 [13:45<145:58:44, 81.79s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 54978: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 56287: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 57596: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 58905: cache has only 0 modules

  0%|          | 11/6435 [15:06<145:28:59, 81.53s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 60214: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 61523: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 62832: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 64141: cache has only 0 modules

  0%|          | 12/6435 [16:26<144:34:38, 81.03s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 65450: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 66759: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 68068: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 69377: cache has only 0 modules

  0%|          | 13/6435 [17:51<146:47:03, 82.28s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 70686: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 71995: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 73304: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 74613: cache has only 0 modules

  0%|          | 14/6435 [19:11<145:31:32, 81.59s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 75922: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 77231: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 78540: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 79849: cache has only 0 modules

  0%|          | 15/6435 [20:40<149:26:13, 83.80s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 81158: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 82467: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 83776: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 85085: cache has only 0 modules

  0%|          | 16/6435 [22:00<147:23:44, 82.66s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 86394: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 87703: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 89012: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 90321: cache has only 0 modules

  0%|          | 17/6435 [23:22<147:03:30, 82.49s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 91630: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 92939: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=200):
   EW-based hits: 32 (16.0%)
   Fallback hits: 7 (3.5%)
   Overall hit rate: 19.5%
   Misses: 161 (80.5%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 94248: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 95557: cache has only 0 modules

  0%|          | 18/6435 [24:41<145:33:25, 81.66s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 96866: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 98175: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=200):
   EW-based hits: 31 (15.5%)
   Fallback hits: 15 (7.5%)
   Overall hit rate: 23.0%
   Misses: 154 (77.0%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 99484: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 100793: cache has only 0 modules

  0%|          | 19/6435 [26:01<144:09:36, 80.89s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 102102: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 103411: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 104720: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 106029: cache has only 0 modules

  0%|          | 20/6435 [27:25<146:07:56, 82.01s/it]
                                                     
{'loss': 5.1248, 'grad_norm': 1245.8405863307346, 'learning_rate': 9.500000000000001e-07, 'completion_length': 476.8609375, 'grpo_advantage': -3.777422380447388, 'format_advantage': -0.02500000037252903, 'length_advantage': 0.0, 'kl_divergence': 0.0030746936798095705, 'adaptive_beta': np.float64(0.09991904264336762), 'easy_ratio': 0.0, 'accuracy': 0.03546875100582838, 'rewards/emotion_accuracy_reward': 0.12968750442378224, 'rewards/emotion_format_reward': 0.0234375, 'reward': 0.1531250037252903, 'reward_std': 0.2103672094643116, 'kl': 0.0025612354278564454, 'epoch': 0.01}

  0%|          | 20/6435 [27:25<146:07:56, 82.01s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 107338: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 108647: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 109956: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 111265: cache has only 0 modules

  0%|          | 21/6435 [28:48<146:22:06, 82.15s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 112574: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 113883: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 115192: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 116501: cache has only 0 modules

  0%|          | 22/6435 [30:09<146:08:23, 82.04s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 117810: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 119119: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 120428: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 121737: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  0%|          | 23/6435 [31:30<145:06:28, 81.47s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 123046: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 124355: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 125664: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 126973: cache has only 0 modules

  0%|          | 24/6435 [32:50<144:44:42, 81.28s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 128282: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 129591: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 130900: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 132209: cache has only 0 modules

  0%|          | 25/6435 [34:10<143:43:19, 80.72s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 133518: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=300):
   EW-based hits: 51 (17.0%)
   Fallback hits: 13 (4.3%)
   Overall hit rate: 21.3%
   Misses: 236 (78.7%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 134827: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 136136: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 137445: cache has only 0 modules

  0%|          | 26/6435 [35:33<144:53:06, 81.38s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 138754: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 140063: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 141372: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=300):
   EW-based hits: 51 (17.0%)
   Fallback hits: 19 (6.3%)
   Overall hit rate: 23.3%
   Misses: 230 (76.7%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 142681: cache has only 0 modules

  0%|          | 27/6435 [36:56<145:54:07, 81.97s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 143990: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 145299: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 146608: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 147917: cache has only 0 modules

  0%|          | 28/6435 [38:16<144:49:01, 81.37s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 149226: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 150535: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 151844: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 153153: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  0%|          | 29/6435 [39:38<145:22:15, 81.69s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 154462: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 155771: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 157080: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 158389: cache has only 0 modules

  0%|          | 30/6435 [41:00<145:00:14, 81.50s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 159698: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 161007: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 162316: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 163625: cache has only 0 modules

  0%|          | 31/6435 [42:22<145:43:58, 81.92s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 164934: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 166243: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 167552: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 168861: cache has only 0 modules

  0%|          | 32/6435 [43:41<143:41:39, 80.79s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 170170: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 171479: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 172788: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 174097: cache has only 0 modules

  1%|          | 33/6435 [44:59<142:33:36, 80.17s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 175406: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 176715: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=400):
   EW-based hits: 67 (16.8%)
   Fallback hits: 21 (5.2%)
   Overall hit rate: 22.0%
   Misses: 312 (78.0%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 178024: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 179333: cache has only 0 modules

  1%|          | 34/6435 [46:21<143:25:30, 80.66s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 180642: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 181951: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=400):
   EW-based hits: 74 (18.5%)
   Fallback hits: 23 (5.8%)
   Overall hit rate: 24.2%
   Misses: 303 (75.8%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 183260: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 184569: cache has only 0 modules

  1%|          | 35/6435 [47:43<143:59:57, 81.00s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 185878: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 187187: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 188496: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 189805: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 36/6435 [49:03<143:39:33, 80.82s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 191114: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 192423: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 193732: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 195041: cache has only 0 modules

  1%|          | 37/6435 [50:22<142:20:43, 80.09s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 196350: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 197659: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 198968: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 200277: cache has only 0 modules

  1%|          | 38/6435 [51:43<142:49:36, 80.38s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 201586: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 202895: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 204204: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 205513: cache has only 0 modules

  1%|          | 39/6435 [53:04<143:27:35, 80.75s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 206822: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 208131: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 209440: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 210749: cache has only 0 modules

  1%|          | 40/6435 [54:28<144:48:34, 81.52s/it]
                                                     
{'loss': -0.5055, 'grad_norm': 1082.4619807476674, 'learning_rate': 1.9500000000000004e-06, 'completion_length': 477.7921875, 'grpo_advantage': 0.7869734764099121, 'format_advantage': -0.02500000037252903, 'length_advantage': 0.0, 'kl_divergence': 0.03631324768066406, 'adaptive_beta': np.float64(0.0997745593875374), 'easy_ratio': 0.0, 'accuracy': 0.028437500819563866, 'rewards/emotion_accuracy_reward': 0.1661718800198287, 'rewards/emotion_format_reward': 0.0453125, 'reward': 0.2114843787625432, 'reward_std': 0.2558738960884511, 'kl': 0.02782917022705078, 'epoch': 0.02}

  1%|          | 40/6435 [54:28<144:48:34, 81.52s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 212058: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 213367: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 214676: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=500):
   EW-based hits: 85 (17.0%)
   Fallback hits: 23 (4.6%)
   Overall hit rate: 21.6%
   Misses: 392 (78.4%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 215985: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 41/6435 [55:51<145:45:34, 82.07s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 217294: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 218603: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 219912: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=500):
   EW-based hits: 81 (16.2%)
   Fallback hits: 29 (5.8%)
   Overall hit rate: 22.0%
   Misses: 390 (78.0%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 221221: cache has only 0 modules

  1%|          | 42/6435 [57:13<145:31:44, 81.95s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 222530: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 223839: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 225148: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 226457: cache has only 0 modules

  1%|          | 43/6435 [58:33<144:51:12, 81.58s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 227766: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 229075: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 230384: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 231693: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 44/6435 [59:53<143:35:40, 80.89s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])Video tensor shape: torch.Size([8, 3, 224, 224])

It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 233002: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 234311: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 235620: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 236929: cache has only 0 modules

  1%|          | 45/6435 [1:01:12<142:57:19, 80.54s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 238238: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 239547: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 240856: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 242165: cache has only 0 modules

  1%|          | 46/6435 [1:02:34<143:32:33, 80.88s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 243474: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 244783: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 246092: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 247401: cache has only 0 modules

  1%|          | 47/6435 [1:03:54<142:55:21, 80.55s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 248710: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 250019: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 251328: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=600):
   EW-based hits: 105 (17.5%)
   Fallback hits: 29 (4.8%)
   Overall hit rate: 22.3%
   Misses: 466 (77.7%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 252637: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 48/6435 [1:05:15<143:19:45, 80.79s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 253946: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=600):
   EW-based hits: 99 (16.5%)
   Fallback hits: 31 (5.2%)
   Overall hit rate: 21.7%
   Misses: 470 (78.3%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 255255: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 256564: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 257873: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 49/6435 [1:06:42<146:33:02, 82.62s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 259182: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 260491: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 261800: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 263109: cache has only 0 modules

  1%|          | 50/6435 [1:08:05<146:57:24, 82.86s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 264418: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 265727: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 267036: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 268345: cache has only 0 modules

  1%|          | 51/6435 [1:09:35<150:37:25, 84.94s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 269654: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 270963: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 272272: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 273581: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 52/6435 [1:11:03<151:56:25, 85.69s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 274890: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 276199: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 277508: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Video tensor shape: torch.Size([8, 3, 224, 224])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 278817: cache has only 0 modules

  1%|          | 53/6435 [1:12:21<147:55:21, 83.44s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 280126: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 281435: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 282744: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: TrueVisual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has image_mean: True

Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 284053: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=700):
   EW-based hits: 117 (16.7%)
   Fallback hits: 31 (4.4%)
   Overall hit rate: 21.1%
   Misses: 552 (78.9%)

  1%|          | 54/6435 [1:13:40<145:45:17, 82.23s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 285362: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 286671: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 287980: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=700):
   EW-based hits: 123 (17.6%)
   Fallback hits: 36 (5.1%)
   Overall hit rate: 22.7%
   Misses: 541 (77.3%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 289289: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 55/6435 [1:15:02<145:36:46, 82.16s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 290598: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 291907: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 293216: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 294525: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 56/6435 [1:16:22<144:06:38, 81.33s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 295834: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 297143: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 298452: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 299761: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True

  1%|          | 57/6435 [1:17:49<147:13:55, 83.10s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 301070: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 302379: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 303688: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 304997: cache has only 0 modules

  1%|          | 58/6435 [1:19:12<147:23:08, 83.20s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 306306: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 307615: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 308924: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 310233: cache has only 0 modules

  1%|          | 59/6435 [1:20:34<146:43:19, 82.84s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 311542: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 312851: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 314160: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 315469: cache has only 0 modules

  1%|          | 60/6435 [1:21:57<146:21:21, 82.65s/it]
                                                       
{'loss': 3.8614, 'grad_norm': 1124.652859960441, 'learning_rate': 2.95e-06, 'completion_length': 491.5921875, 'grpo_advantage': -7.203879117965698, 'format_advantage': -0.02500000037252903, 'length_advantage': 0.0, 'kl_divergence': 0.04175834655761719, 'adaptive_beta': np.float64(0.09964443743399737), 'easy_ratio': 0.0, 'accuracy': 0.07125000096857548, 'rewards/emotion_accuracy_reward': 0.2682031264528632, 'rewards/emotion_format_reward': 0.575, 'reward': 0.843203118443489, 'reward_std': 0.4110146876424551, 'kl': 0.13581504821777343, 'epoch': 0.03}

  1%|          | 60/6435 [1:21:57<146:21:21, 82.65s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 316778: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=800):
   EW-based hits: 129 (16.1%)
   Fallback hits: 35 (4.4%)
   Overall hit rate: 20.5%
   Misses: 636 (79.5%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 318087: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 319396: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 320705: cache has only 0 modules

  1%|          | 61/6435 [1:23:20<146:42:53, 82.86s/it]Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 322014: cache has only 0 modules
📊 Open Vocabulary Hit Rate Statistics (n=800):
   EW-based hits: 148 (18.5%)
   Fallback hits: 46 (5.8%)
   Overall hit rate: 24.2%
   Misses: 606 (75.8%)
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Invalidate trace cache @ step 0 and module 323323: cache has only 0 modules
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Visual processor type: <class 'transformers.models.siglip.image_processing_siglip.SiglipImageProcessor'>
Has preprocess method: True
Has image_mean: True
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
Video tensor shape: torch.Size([8, 3, 224, 224])
It is strongly recommended to pass the `sampling_rate` argument to `WhisperFeatureExtractor()`. Failing to do so can result in silent errors that might be hard to debug.
Extracted audio input_features shape: (1, 128, 3000)
Converted audio to torch tensor shape: torch.Size([1, 128, 3000])
W0728 18:35:19.042200 49159 site-packages/torch/distributed/elastic/agent/server/api.py:704] Received Signals.SIGINT death signal, shutting down workers
W0728 18:35:19.044223 49159 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 49236 closing signal SIGINT
W0728 18:35:19.044855 49159 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 49237 closing signal SIGINT
[rank1]: Traceback (most recent call last):
[rank1]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1/train_emotion_easy_hard_grpo.py", line 252, in <module>
[rank1]:     success = main()
[rank1]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1/train_emotion_easy_hard_grpo.py", line 215, in main
[rank1]:     trainer.train()
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank1]:     return inner_training_loop(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
[rank1]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
[rank1]:     loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
[rank1]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 779, in compute_loss
[rank1]:     prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank1]:     return func(*args, **kwargs)
[rank1]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
[rank1]:     return super().generate(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank1]:     return func(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
[rank1]:     result = self._sample(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
[rank1]:     outputs = model_forward(**model_inputs, return_dict=True)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1844, in _call_impl
[rank1]:     return inner()
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1790, in inner
[rank1]:     result = forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
[rank1]:     outputs = super().forward(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank1]:     output = func(self, *args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
[rank1]:     outputs: BaseModelOutputWithPast = self.model(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank1]:     output = func(self, *args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
[rank1]:     layer_outputs = decoder_layer(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
[rank1]:     return super().__call__(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 236, in forward
[rank1]:     hidden_states, self_attn_weights = self.self_attn(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 170, in forward
[rank1]:     attn_output, attn_weights = attention_interface(
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/integrations/sdpa_attention.py", line 41, in sdpa_attention_forward
[rank1]:     key = repeat_kv(key, module.num_key_value_groups)
[rank1]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/integrations/sdpa_attention.py", line 20, in repeat_kv
[rank1]:     return hidden_states.reshape(batch, num_key_value_heads * n_rep, slen, head_dim)
[rank1]: KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1/train_emotion_easy_hard_grpo.py", line 252, in <module>
[rank0]:     success = main()
[rank0]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1/train_emotion_easy_hard_grpo.py", line 215, in main
[rank0]:     trainer.train()
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank0]:     return inner_training_loop(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
[rank0]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
[rank0]:     loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
[rank0]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 779, in compute_loss
[rank0]:     prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
[rank0]:     return super().generate(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
[rank0]:     result = self._sample(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
[rank0]:     outputs = model_forward(**model_inputs, return_dict=True)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1844, in _call_impl
[rank0]:     return inner()
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1790, in inner
[rank0]:     result = forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/MERGE_GRPO/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
[rank0]:     outputs = super().forward(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank0]:     output = func(self, *args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
[rank0]:     outputs: BaseModelOutputWithPast = self.model(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank0]:     output = func(self, *args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
[rank0]:     layer_outputs = decoder_layer(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
[rank0]:     return super().__call__(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 236, in forward
[rank0]:     hidden_states, self_attn_weights = self.self_attn(
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 154, in forward
[rank0]:     query_states = self.q_proj(hidden_states).view(hidden_shape).transpose(1, 2)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/nn/modules/linear.py", line 125, in forward
[rank0]:     return F.linear(input, self.weight, self.bias)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/deepspeed/runtime/zero/linear.py", line 118, in zero3_linear_wrap
[rank0]:     return LinearFunctionForZeroStage3.apply(input, weight, bias)
[rank0]:   File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/autograd/function.py", line 575, in apply
[rank0]:     return super().apply(*args, **kwargs)  # type: ignore[misc]
[rank0]: KeyboardInterrupt

  1%|          | 61/6435 [1:24:21<146:54:36, 82.97s/it]
W0728 18:35:21.441574 49159 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 49236 closing signal SIGTERM
Traceback (most recent call last):
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 696, in run
    result = self._invoke_run(role)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 855, in _invoke_run
    time.sleep(monitor_interval)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 84, in _terminate_process_handler
    raise SignalException(f"Process {os.getpid()} got signal: {sigval}", sigval=sigval)
torch.distributed.elastic.multiprocessing.api.SignalException: Process 49159 got signal: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data/wuyang/conda_envs/r1-omni/bin/torchrun", line 8, in <module>
    sys.exit(main())
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py", line 355, in wrapper
    return f(*args, **kwargs)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/run.py", line 919, in main
    run(args)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/run.py", line 910, in run
    elastic_launch(
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 138, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 260, in launch_agent
    result = agent.run()
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/metrics/api.py", line 137, in wrapper
    result = f(*args, **kwargs)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 705, in run
    self._shutdown(e.sigval)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/local_elastic_agent.py", line 365, in _shutdown
    self._pcontext.close(death_sig)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 572, in close
    self._close(death_sig=death_sig, timeout=timeout)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 909, in _close
    handler.proc.wait(time_to_wait)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/subprocess.py", line 1207, in wait
    return self._wait(timeout=timeout)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/subprocess.py", line 1935, in _wait
    time.sleep(delay)
  File "/data/wuyang/conda_envs/r1-omni/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 84, in _terminate_process_handler
    raise SignalException(f"Process {os.getpid()} got signal: {sigval}", sigval=sigval)
torch.distributed.elastic.multiprocessing.api.SignalException: Process 49159 got signal: 2
