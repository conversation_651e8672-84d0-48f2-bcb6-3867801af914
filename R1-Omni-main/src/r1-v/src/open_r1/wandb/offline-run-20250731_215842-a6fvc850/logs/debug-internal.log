{"time":"2025-07-31T21:58:42.254016243+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T21:58:42.381909446+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T21:58:42.382198251+08:00","level":"INFO","msg":"stream: created new stream","id":"a6fvc850"}
{"time":"2025-07-31T21:58:42.382240229+08:00","level":"INFO","msg":"stream: started","id":"a6fvc850"}
{"time":"2025-07-31T21:58:42.382317569+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"a6fvc850"}
{"time":"2025-07-31T21:58:42.382367924+08:00","level":"INFO","msg":"handler: started","stream_id":"a6fvc850"}
{"time":"2025-07-31T21:58:42.382421683+08:00","level":"INFO","msg":"sender: started","stream_id":"a6fvc850"}
{"time":"2025-07-31T21:58:42.383364835+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
