{"time":"2025-07-31T22:07:00.37840146+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T22:07:00.506116468+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T22:07:00.506464376+08:00","level":"INFO","msg":"stream: created new stream","id":"wdfob2up"}
{"time":"2025-07-31T22:07:00.506513502+08:00","level":"INFO","msg":"stream: started","id":"wdfob2up"}
{"time":"2025-07-31T22:07:00.506638468+08:00","level":"INFO","msg":"sender: started","stream_id":"wdfob2up"}
{"time":"2025-07-31T22:07:00.506605578+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wdfob2up"}
{"time":"2025-07-31T22:07:00.506734134+08:00","level":"INFO","msg":"handler: started","stream_id":"wdfob2up"}
{"time":"2025-07-31T22:07:00.507908479+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
