wheel==0.45.1
av==10.0.0
mpmath==1.3.0
typing_extensions==4.12.2
sympy==1.13.1
pillow==11.0.0
nvidia-nvtx-cu12==12.4.127
nvidia-nvjitlink-cu12==12.4.127
nvidia-nccl-cu12==2.21.5
nvidia-curand-cu12==10.3.5.147
nvidia-cufft-cu12==11.2.1.3
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cublas-cu12==12.4.5.8
networkx==3.3
MarkupSafe==2.1.5
fsspec==2024.6.1
filelock==3.13.1
triton==3.1.0
nvidia-cusparse-cu12==12.3.1.170
nvidia-cudnn-cu12==9.1.0.70
Jinja2==3.1.4
nvidia-cusolver-cu12==11.6.1.9
torch==2.5.1+cu124
torchvision==0.20.1+cu124
torchaudio==2.5.1+cu124
urllib3==2.5.0
tqdm==4.67.1
safetensors==0.5.3
regex==2024.11.6
PyYAML==6.0.2
packaging==25.0
idna==3.10
hf-xet==1.1.5
charset-normalizer==3.4.2
certifi==2025.7.9
requests==2.32.4
huggingface-hub==0.33.2
tokenizers==0.21.2
einops==0.8.1
flash-attn==2.7.3
wcwidth==0.2.13
sentencepiece==0.2.0
pytz==2025.2
py-cpuinfo==9.0.0
nvidia-ml-py==12.575.51
hjson==3.1.0
antlr4-python3-runtime==4.9.3
aenum==3.1.15
xxhash==3.5.0
tzdata==2025.2
typing-inspection==0.4.1
tomli==2.2.1
threadpoolctl==3.6.0
termcolor==2.3.0
tcolorpy==0.1.7
tabulate==0.9.0
sniffio==1.3.1
smmap==5.0.2
six==1.17.0
shellingham==1.5.4
Pygments==2.19.2
pyflakes==3.4.0
pydantic_core==2.33.2
pycountry==24.6.1
pycodestyle==2.14.0
pyarrow==20.0.0
psutil==7.0.0
protobuf==6.31.1
propcache==0.3.2
prompt_toolkit==3.0.51
portalocker==3.2.0
pluggy==1.6.0
platformdirs==4.3.8
pfzy==0.3.4
pathvalidate==3.3.1
pathspec==0.12.1
parameterized==0.9.0
mbstrdecoder==1.1.4
ninja==********
mypy_extensions==1.1.0
multidict==6.6.3
msgpack==1.1.1
mdurl==0.1.2
mccabe==0.7.0
lxml==6.0.0
joblib==1.5.1
isort==6.0.1
iniconfig==2.1.0
hf_transfer==0.1.9
h11==0.16.0
frozenlist==1.7.0
exceptiongroup==1.3.0
dill==0.3.8
colorlog==6.9.0
colorama==0.4.6
click==8.2.1
chardet==5.2.0
attrs==25.3.0
async-timeout==5.0.1
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
absl-py==2.3.1
yarl==1.20.1
scipy==1.15.3
sacrebleu==2.5.1
python-dateutil==2.9.0.post0
pytest==8.4.1
pydantic==2.11.7
nltk==3.9.1
multiprocess==0.70.16
markdown-it-py==3.0.0
latex2sympy2_extended==1.0.6
inquirerpy==0.3.4
httpcore==1.0.9
gitdb==4.0.12
flake8==7.3.0
black==25.1.0
anyio==4.9.0
aiosignal==1.4.0
typepy==1.3.4
scikit-learn==1.7.0
rouge_score==0.1.2
rich==14.0.0
pandas==2.3.1
math-verify==0.5.2
httpx==0.27.2
GitPython==3.1.44
bitsandbytes==0.42.0
aiohttp==3.12.13
typer==0.16.0
transformers==4.53.1
liger_kernel==0.5.2
deepspeed==0.15.4
accelerate==1.8.1
datasets==4.0.0
DataProperty==1.1.0
trl==0.14.0
tabledata==1.3.4
pytablewriter==1.2.1
lighteval==0.10.0
h5py==3.14.0
wandb==0.21.0
setuptools==80.9.0
pip==25.1.1
qwen-vl-utils==0.0.11
python-dotenv==1.1.1
proglog==0.1.12
imageio-ffmpeg==0.6.0
imageio==2.37.0
ipdb==0.13.13
decorator==4.4.2
moviepy==1.0.3
timm==1.0.16
numpy==2.2.6
opencv-python==*********
decord==0.6.0
pure_eval==0.2.3
ptyprocess==0.7.0
traitlets==5.14.3
pexpect==4.9.0
parso==0.8.4
executing==2.2.0
asttokens==3.0.0
stack-data==0.6.3
matplotlib-inline==0.1.7
jedi==0.19.2
ipython==8.37.0
sentry-sdk==2.32.0
omegaconf==2.3.0
braceexpand==0.1.7
webdataset==1.0.2
iopath==0.1.10
ftfy==6.3.1
yacs==0.1.8
fvcore==0.1.5.post20221221
pytorchvideo==0.1.5
jiter==0.10.0
distro==1.9.0
openai==1.97.1
pyparsing==3.2.3
kiwisolver==1.4.8
fonttools==4.59.0
cycler==0.12.1
contourpy==1.3.2
matplotlib==3.10.3
et_xmlfile==2.0.0
openpyxl==3.1.5
Werkzeug==3.1.3
tensorboard-data-server==0.7.2
Markdown==3.8.2
grpcio==1.74.0
tensorboard==2.20.0
r1-v==0.1.0
platformdirs==4.2.2
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
