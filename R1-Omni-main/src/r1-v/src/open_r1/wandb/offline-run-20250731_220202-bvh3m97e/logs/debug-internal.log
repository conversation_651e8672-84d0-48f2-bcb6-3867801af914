{"time":"2025-07-31T22:02:02.260896273+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T22:02:02.390693432+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T22:02:02.390912028+08:00","level":"INFO","msg":"stream: created new stream","id":"bvh3m97e"}
{"time":"2025-07-31T22:02:02.390941197+08:00","level":"INFO","msg":"stream: started","id":"bvh3m97e"}
{"time":"2025-07-31T22:02:02.391032335+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"bvh3m97e"}
{"time":"2025-07-31T22:02:02.391084576+08:00","level":"INFO","msg":"handler: started","stream_id":"bvh3m97e"}
{"time":"2025-07-31T22:02:02.391161944+08:00","level":"INFO","msg":"sender: started","stream_id":"bvh3m97e"}
{"time":"2025-07-31T22:02:02.391869956+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
